<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .start-end { fill: #e1f5fe; stroke: #01579b; stroke-width: 2; }
      .process { fill: #f3e5f5; stroke: #4a148c; stroke-width: 2; }
      .decision { fill: #fff3e0; stroke: #e65100; stroke-width: 2; }
      .success { fill: #e8f5e8; stroke: #1b5e20; stroke-width: 2; }
      .failure { fill: #ffebee; stroke: #b71c1c; stroke-width: 2; }
      .sort { fill: #e3f2fd; stroke: #1565c0; stroke-width: 2; }
      .time { fill: #f1f8e9; stroke: #33691e; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; dominant-baseline: middle; }
      .title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .label { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" class="title" style="font-size: 18px;">findCrbtTemplateId 方法流程图</text>
  
  <!-- Start -->
  <rect x="600" y="60" width="200" height="40" rx="20" class="start-end"/>
  <text x="700" y="80" class="text">开始 findCrbtTemplateId</text>
  
  <!-- Get templates -->
  <rect x="550" y="130" width="300" height="50" rx="5" class="process"/>
  <text x="700" y="150" class="text">获取炫铃时间策略模板</text>
  <text x="700" y="165" class="text" style="font-size: 10px;">templateService.findCrbtTimeTemplate()</text>
  
  <!-- Check if templates empty -->
  <polygon points="700,220 780,250 700,280 620,250" class="decision"/>
  <text x="700" y="245" class="text">模板列表</text>
  <text x="700" y="260" class="text">是否为空?</text>
  
  <!-- Return null if empty -->
  <rect x="900" y="230" width="120" height="40" rx="5" class="failure"/>
  <text x="960" y="250" class="text">返回 null</text>
  
  <!-- Sort templates -->
  <rect x="550" y="330" width="300" height="60" rx="5" class="sort"/>
  <text x="700" y="350" class="text">模板排序和随机化</text>
  <text x="700" y="365" class="text" style="font-size: 10px;">按优先级排序 + 随机打乱</text>
  <text x="700" y="380" class="text" style="font-size: 10px;">orderList + randomList</text>
  
  <!-- Initialize time variables -->
  <rect x="550" y="420" width="300" height="80" rx="5" class="time"/>
  <text x="700" y="440" class="text">初始化时间变量</text>
  <text x="700" y="455" class="text" style="font-size: 10px;">now = new Date()</text>
  <text x="700" y="470" class="text" style="font-size: 10px;">nowTime = formatShortTime(now)</text>
  <text x="700" y="485" class="text" style="font-size: 10px;">week, preWeek, isHoliday</text>
  
  <!-- Loop through templates -->
  <rect x="550" y="530" width="300" height="40" rx="5" class="process"/>
  <text x="700" y="550" class="text">遍历时间策略模板列表</text>
  
  <!-- Check time with template -->
  <polygon points="700,610 800,650 700,690 600,650" class="decision"/>
  <text x="700" y="640" class="text">checkTimeWithTemplate</text>
  <text x="700" y="655" class="text" style="font-size: 10px;">时间匹配检查</text>
  <text x="700" y="670" class="text" style="font-size: 10px;">是否匹配?</text>
  
  <!-- Return template ID -->
  <rect x="950" y="630" width="150" height="40" rx="5" class="success"/>
  <text x="1025" y="650" class="text">返回 templateId</text>
  
  <!-- Continue loop -->
  <rect x="400" y="630" width="120" height="40" rx="5" class="process"/>
  <text x="460" y="650" class="text">继续下一个模板</text>
  
  <!-- No match found -->
  <rect x="600" y="750" width="200" height="40" rx="5" class="failure"/>
  <text x="700" y="770" class="text">所有模板都不匹配</text>
  
  <!-- Return null final -->
  <rect x="600" y="820" width="200" height="40" rx="5" class="failure"/>
  <text x="700" y="840" class="text">返回 null</text>
  
  <!-- End -->
  <rect x="600" y="890" width="200" height="40" rx="20" class="start-end"/>
  <text x="700" y="910" class="text">方法结束</text>
  
  <!-- checkTimeWithTemplate detail box -->
  <rect x="50" y="980" width="600" height="280" rx="5" fill="#f9f9f9" stroke="#666" stroke-width="1"/>
  <text x="350" y="1005" class="title" style="font-size: 14px;">checkTimeWithTemplate 详细逻辑</text>
  
  <!-- Time strategy types -->
  <text x="70" y="1030" class="text" style="font-size: 11px; text-anchor: start;">1. 检查模板是否启用 (timePlan.isChecked())</text>
  
  <text x="70" y="1055" class="text" style="font-size: 11px; text-anchor: start;">2. 时间策略类型判断:</text>
  <text x="90" y="1075" class="text" style="font-size: 10px; text-anchor: start;">• CUSTOM(9): 自定义时间段 - 检查日期时间范围</text>
  <text x="90" y="1095" class="text" style="font-size: 10px; text-anchor: start;">• WORKDAY(8): 周一到周五 - 检查工作日时间</text>
  <text x="90" y="1115" class="text" style="font-size: 10px; text-anchor: start;">• ALLDAY(0): 全部时间 - 检查时间范围</text>
  <text x="90" y="1135" class="text" style="font-size: 10px; text-anchor: start;">• HOLIDAY(10): 节假日 - 检查节假日时间</text>
  <text x="90" y="1155" class="text" style="font-size: 10px; text-anchor: start;">• NOTHOLIDAY(11): 非节假日 - 检查非节假日时间</text>
  <text x="90" y="1175" class="text" style="font-size: 10px; text-anchor: start;">• 具体星期(1-7): 检查对应星期的时间</text>
  
  <text x="70" y="1200" class="text" style="font-size: 11px; text-anchor: start;">3. 时间比较方法:</text>
  <text x="90" y="1220" class="text" style="font-size: 10px; text-anchor: start;">• satisfyTime(): 普通时间范围检查</text>
  <text x="90" y="1240" class="text" style="font-size: 10px; text-anchor: start;">• compareToAcrossWeekTime(): 跨周时间检查</text>
  
  <!-- WeekEnum constants -->
  <rect x="700" y="980" width="650" height="280" rx="5" fill="#f0f8ff" stroke="#666" stroke-width="1"/>
  <text x="1025" y="1005" class="title" style="font-size: 14px;">WeekEnum 常量定义</text>
  
  <text x="720" y="1030" class="text" style="font-size: 10px; text-anchor: start;">MONDAY(1) - 星期一      TUESDAY(2) - 星期二</text>
  <text x="720" y="1050" class="text" style="font-size: 10px; text-anchor: start;">WEDNESDAY(3) - 星期三   THURSDAY(4) - 星期四</text>
  <text x="720" y="1070" class="text" style="font-size: 10px; text-anchor: start;">FRIDAY(5) - 星期五      SATURDAY(6) - 星期六</text>
  <text x="720" y="1090" class="text" style="font-size: 10px; text-anchor: start;">SUNDAY(7) - 星期日</text>
  
  <text x="720" y="1120" class="text" style="font-size: 10px; text-anchor: start;">ALLDAY(0) - 全部时间    WORKDAY(8) - 周一到周五</text>
  <text x="720" y="1140" class="text" style="font-size: 10px; text-anchor: start;">CUSTOM(9) - 自定义      HOLIDAY(10) - 节假日</text>
  <text x="720" y="1160" class="text" style="font-size: 10px; text-anchor: start;">NOTHOLIDAY(11) - 非节假日</text>
  
  <text x="720" y="1190" class="text" style="font-size: 11px; text-anchor: start;">排序优先级:</text>
  <text x="720" y="1210" class="text" style="font-size: 10px; text-anchor: start;">• HOLIDAY(10): 优先级 1 (最高)</text>
  <text x="720" y="1230" class="text" style="font-size: 10px; text-anchor: start;">• CUSTOM(9): 优先级 2</text>
  <text x="720" y="1250" class="text" style="font-size: 10px; text-anchor: start;">• 其他: 优先级 0 (随机排序)</text>
  
  <!-- Arrows -->
  <line x1="700" y1="100" x2="700" y2="130" class="arrow"/>
  <line x1="700" y1="180" x2="700" y2="220" class="arrow"/>
  
  <line x1="780" y1="250" x2="900" y2="250" class="arrow"/>
  <text x="840" y="240" class="label">YES</text>
  
  <line x1="700" y1="280" x2="700" y2="330" class="arrow"/>
  <text x="710" y="310" class="label">NO</text>
  
  <line x1="700" y1="390" x2="700" y2="420" class="arrow"/>
  <line x1="700" y1="500" x2="700" y2="530" class="arrow"/>
  <line x1="700" y1="570" x2="700" y2="610" class="arrow"/>
  
  <line x1="800" y1="650" x2="950" y2="650" class="arrow"/>
  <text x="870" y="640" class="label">YES</text>
  
  <line x1="600" y1="650" x2="520" y2="650" class="arrow"/>
  <text x="550" y="640" class="label">NO</text>
  
  <!-- Loop back arrow -->
  <path d="M 460 630 Q 460 580 460 550 Q 460 520 550 550" class="arrow"/>
  <text x="480" y="590" class="label">继续循环</text>
  
  <!-- No more templates -->
  <line x1="700" y1="690" x2="700" y2="750" class="arrow"/>
  <text x="710" y="720" class="label">遍历完成</text>
  
  <line x1="700" y1="790" x2="700" y2="820" class="arrow"/>
  <line x1="700" y1="860" x2="700" y2="890" class="arrow"/>
  
  <!-- From success to end -->
  <path d="M 1025 670 Q 1025 910 800 910" class="arrow"/>
  
  <!-- From first null to end -->
  <path d="M 960 270 Q 960 910 800 910" class="arrow"/>
  
  <!-- Method summary box -->
  <rect x="50" y="1300" width="1300" height="120" rx="5" fill="#fff9c4" stroke="#f57f17" stroke-width="2"/>
  <text x="700" y="1325" class="title" style="font-size: 14px;">方法总结</text>
  
  <text x="70" y="1350" class="text" style="font-size: 11px; text-anchor: start;">1. 获取部门的炫铃时间策略模板列表</text>
  <text x="70" y="1370" class="text" style="font-size: 11px; text-anchor: start;">2. 按优先级排序：节假日(10) > 自定义(9) > 其他(随机)</text>
  <text x="70" y="1390" class="text" style="font-size: 11px; text-anchor: start;">3. 遍历模板，使用当前时间匹配时间策略</text>
  <text x="70" y="1410" class="text" style="font-size: 11px; text-anchor: start;">4. 返回第一个匹配的模板ID，如果都不匹配则返回null</text>
</svg>
