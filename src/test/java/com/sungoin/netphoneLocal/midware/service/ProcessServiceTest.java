/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.util.List;

import javax.annotation.Resource;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.util.JsonHelper;

/**
 * <AUTHOR>
 */
public class ProcessServiceTest extends BaseTest {

    @Resource
    ProcessService instance;

    @Resource
    BaseService baseService;

    public ProcessServiceTest() {
    }

    @BeforeClass
    public static void setUpClass() {
    }

    @AfterClass
    public static void tearDownClass() {
    }

    @Before
    public void setUp() {
    }

    @After
    public void tearDown() {
    }

    @Test
    public void testGetInputParentDept() {
        String ring = instance.getRingFile("400000000", "10000000");
        System.out.println(ring);
    }

    /**
     * Test of getFixedCallNo method, of class ProcessService.
     */
    @Test
    public void testGetFixedCallNo() {
        System.out.println("getFixedCallNo");
        String callNo = "";
        String expResult = "";
        String result = this.instance.getFixedCallerNo(callNo);
        assertEquals(expResult, result);
        // TODO review the generated test code and remove the default call to fail.
        fail("The test case is a prototype.");
    }

    /**
     * Test of getUserByOriginalNo method, of class ProcessService.
     */
    @Test
    public void testGetUserByOriginalNo() {
        System.out.println("getUserByOriginalNo");
        String originalNo = "";
        String callerNo = "";
        User expResult = null;
        User result = this.instance.getUserByOriginalNo(false, originalNo, callerNo);
        assertEquals(expResult, result);
        // TODO review the generated test code and remove the default call to fail.
        fail("The test case is a prototype.");
    }

    /**
     * Test of isCallerPassBlackAndWhiteNo method, of class ProcessService.
     */
    @Test
    public void testIsCallerPassBlackAndWhiteNo() {
        System.out.println("isCallerPassBlackAndWhiteNo");
        User user = this.baseService.getUserByNumber("4008010134");
        String caller = "15000304023";
        boolean expResult = true;
        boolean result = this.instance.isCallerPassBlackAndWhiteNo(user, caller);
        assertEquals(expResult, result);
    }

    /**
     * Test of saveOrUpdateTalkNote method, of class ProcessService.
     */
    @Test
    public void testSaveOrUpdateTalkNote() {
        System.out.println("saveOrUpdateTalkNote");
        TalkNote talkNote = null;
        this.instance.saveOrUpdateTalkNote(talkNote, "traceId");
        // TODO review the generated test code and remove the default call to fail.
        fail("The test case is a prototype.");
    }

    /**
     * Test of getRootDept method, of class ProcessService.
     */
    @Test
    public void testGetRootDept() {
        System.out.println("getRootDept");
        User user = this.baseService.getUserByNumber("4008701208");
        Dept result = this.instance.getRootDept(user);
        System.out.println("dept=" + result);
        assertEquals("4008701208", result.getName());
    }

    /**
     * Test of getRingFile method, of class ProcessService.
     */
    @Test
    public void testGetRingFile() {
        System.out.println("getRingFile");
        User user = this.baseService.getUserByNumber("4008016161");
        Dept dept = this.instance.getRootDept(user);
        String result = this.instance.getRingFile(dept);
        System.out.println("ringFile = " + result);
    }

    /**
     * Test of getDefaultRing method, of class ProcessService.
     */
    @Test
    public void testGetDefaultRing() {
        System.out.println("getDefaultRing");
        String ringName = "";
        String expResult = "";
        String result = this.instance.getDefaultRing(ringName);
        assertEquals(expResult, result);
        // TODO review the generated test code and remove the default call to fail.
        fail("The test case is a prototype.");
    }

    @Test
    public void testJson2List() {
        //        String s = "[{\"customer_no\":\"4008600000\",\"dept_id\":\"11680697171\",\"agent_id\":\"aef45f06-74c2-47f4-a129-5d104222bd04\"}]";
        String s = "[[\"superivr_flag\":null,\"call_flag\":\"1\",\"listens_record\":\"1\",\"ring_flag\":\"0\",\"record_flag\":\"1\",\"activate_time\":\"2016-11-15\",\"upload_record\":\"1\",\"screen_flag\":\"1\",\"ineffective_time\":\"2018-11-15\",\"customer_no\":\"4008600000\",\"no_state\":\"16\",\"voicebox_flag\":\"0\",\"gh_flag\":\"0\",\"agent_flag\":\"1\",\"colorring_flag\":\"0\",\"is_record_play\":\"0\",\"safe_state\":\"0\",\"ivr_flag\":\"1\",\"showno_flag\":\"0\",\"sati_flag\":\"0\",\"orginalNo\":\"34123123123\",\"line_flag\":\"0\",\"pre_offhook\":\"1\",\"voicecode_flag\":\"0\"]]";
        try {
            List<String[]> list = JsonHelper.json2List(s, String[].class);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    @Test
    public void testGetDeptIvrKeyName() {
        Dept dept = this.baseService.getDept("dedc9056-cf30-404f-9fdd-69e692218522");
        String s = this.instance.getDeptIvrKeyName(dept);
        System.out.println(s);
    }
}
