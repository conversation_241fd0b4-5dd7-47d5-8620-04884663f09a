<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
	<Appenders>
		<Console name="STDOUT" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] -%X{traceId} %-5level %c{3.} - %msg%n"/>
		</Console>
        
		<RollingRandomAccessFile name="NETPHONELOCAL" fileName="logs/netphoneLocal.log"
								 filePattern="logs/%d{yyyy-MM-dd}/netphoneLocal-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>

        <!-- 400流程日志 -->
		<RollingRandomAccessFile name="MIDWARE" fileName="logs/midware.log"
								 filePattern="logs/%d{yyyy-MM-dd}/midware-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
        
        <!-- 百度流程日志 -->
        <RollingRandomAccessFile name="BAIDUMIDWARE" fileName="logs/baidu_midware.log"
								 filePattern="logs/%d{yyyy-MM-dd}/baidu_midware-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
        
        <!-- 电梯流程日志 -->
        <RollingRandomAccessFile name="ELEVATORMIDWARE" fileName="logs/elevator_midware.log"
								 filePattern="logs/%d{yyyy-MM-dd}/elevator_midware-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
        
        <!-- 尚景流程日志 -->
        <RollingRandomAccessFile name="SUNGOINMIDWARE" fileName="logs/sungoin_midware.log"
								 filePattern="logs/%d{yyyy-MM-dd}/sungoin_midware-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
        
        <!-- 格瑞流程日志 -->
        <RollingRandomAccessFile name="GREATMIDWARE" fileName="logs/great_midware.log"
								 filePattern="logs/%d{yyyy-MM-dd}/great_midware-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
        
        <!-- CPCC流程日志 -->
        <RollingRandomAccessFile name="CPCCMIDWARE" fileName="logs/cpcc_midware.log"
								 filePattern="logs/%d{yyyy-MM-dd}/cpcc_midware-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
		
<!--		<RollingRandomAccessFile name="CTICLIENT" fileName="logs/cti-client.log"
								 filePattern="logs/%d{yyyy-MM-dd}/cti-client%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} [%t] -%X{traceId} %p %c{3.} %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="20 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>-->
        
	</Appenders>
	<Loggers>
        <!-- 百度流程日志 -->
        <logger name="com.sungoin.netphoneLocal.midware.service.baidu" level="DEBUG" additivity="false">
			<appender-ref ref="BAIDUMIDWARE" />
		</logger>

		<!-- 电梯流程日志 -->
        <logger name="com.sungoin.netphoneLocal.midware.service.elevator" level="DEBUG" additivity="false">
			<appender-ref ref="ELEVATORMIDWARE" />
		</logger>
        
        <!-- 尚景流程日志 -->
        <logger name="com.sungoin.netphoneLocal.midware.service.sungoin" level="DEBUG" additivity="false">
			<appender-ref ref="SUNGOINMIDWARE" />
		</logger>
        
        <!-- 格瑞流程日志 -->
        <logger name="com.sungoin.netphoneLocal.midware.service.great" level="DEBUG" additivity="false">
			<appender-ref ref="GREATMIDWARE" />
		</logger>
        
        <!-- CPCC流程日志 -->
        <logger name="com.sungoin.netphoneLocal.midware.service.cpcc" level="DEBUG" additivity="false">
			<appender-ref ref="CPCCMIDWARE" />
		</logger>
        
        <!-- 400流程日志 -->
		<logger name="com.sungoin.netphoneLocal.midware" level="DEBUG" additivity="false">
			<appender-ref ref="MIDWARE" />
		</logger>
        
		<logger name="com.sungoin.netphoneLocal" level="DEBUG" additivity="false">
			<appender-ref ref="NETPHONELOCAL" />
		</logger> 
		
<!--		<logger name="com.sungoin.cti.client" level="DEBUG" additivity="false">
			<appender-ref ref="CTICLIENT" />
		</logger> -->
		
		<Root level="WARN">
			<AppenderRef ref="STDOUT"/>
		</Root>
	</Loggers>
</Configuration>