/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware;

/**
 * <AUTHOR> 2015-8-4
 */
public class MidwareConstants {

    public static final int CHILDTYPE_DIVIDE = 1;//分流
    public static final int CHILDTYPE_INPUT = 0;//按键

    public static final String RECEIVED_DTMF = "dtmf";
    public static final String RECEIVED_DTMF_TIME = "dtmfTime";
    public static final String MAIN_CALL_THREAD = "mainCallThread";
    public static final String CALL_TYPE = "callType";
    public static final String CALL_TYPE_CALLER = "caller";
    public static final String CALL_TYPE_CALLEE = "callee";
    public static final String CALL_TYPE_TRANS = "trans";
    public static final String CALL_TYPE_ROBOT = "robot";
    public static final String CALL_TYPE_OUTCALL = "outCall";
    public static final String CALL_BINDPHONE_ID = "callBindPhone";
    public static final String CALL_BINDPHONE_LSH = "callBindPhoneLsh";
    public static final String CALL_HAS_NEXT = "callHasNext";
	public static final String CALL_AGENT_ID = "callAgentId";

    public static final String DATA_PATH_DEFAULT = "default";
    public static final String DATA_PATH_RINGS = "rings";
    public static final String DATA_PATH_RECORDS = "records";
    public static final String DATA_PATH_BOXES = "boxes";
    public static final String DATA_PATH_CUSTOM = "custom";

    //default ring names
    public static final String DEFAULT_RING_DUDU = "dudu.wav";
    public static final String DEFAULT_RING_SCORE = "score.wav";
    public static final String DEFAULT_RING_SCORE_END = "scoreEnd.wav";
    public static final String DEFAULT_RING_TRANSFER = "transfer.wav";
    public static final String DEFAULT_RING_TRANSFERIVR = "transfer_ivr.wav";
    public static final String DEFAULT_RING_TRANSFER_DEPT = "transfer_dept.wav";
    public static final String DEFAULT_RING_TRANSFER_DEPT_CODE = "100000000";
    public static final String DEFAULT_RING_VOICEBOX = "VoiceBox.wav";
    public static final String DEFAULT_RING_VOICEBOXBYE = "boxesbye.wav";
    public static final String DEFAULT_RING_TIPRECORD = "TipRecord.wav";
    public static final String DEFAULT_RING_WAIT = "wait.wav";
    public static final String DEFAULT_RING_FAIL = "CallFailed.wav";
    public static final String DEFAULT_RING_INPUTERROR = "InputError.wav";
    public static final String DEFAULT_RING_IVRERRORQUIT = "ivrErrorQuit.wav";
    public static final String DEFAULT_RING_NUMHEAD = "empno.wav";
    public static final String DEFAULT_RING_NUMEND = "service.wav";
    public static final String DEFAULT_RING_QUEUEBG = "queuewaitmusic.wav";
    public static final String DEFAULT_RING_QUEUEHEAD = "queuewaitfirst.wav";
    public static final String DEFAULT_RING_QUEUEEND1 = "queuewaitsecond.wav";
    public static final String DEFAULT_RING_QUEUEEND2 = "queuewaitsecond_2.wav";
    public static final String DEFAULT_RING_HOLD = "holdwaitmusic.wav";
    public static final String DEFAULT_RING_IVR = "DefaultIVR.wav";
    public static final String DEFAULT_RING_TIMEOUT_INPUTERROR = "timeoutInputError.wav";
    public static final String DEFAULT_RING_RECORD_TIP = "TipRecord.wav";
    public static final String DEFAULT_RING_AGENT_TIP = "agentTip.wav";
    public static final String DEFAULT_RING_AGENT_TIP_CODE = "100000000";
    public static final String DEFAULT_RING_QUEUETIP = "queuetip.wav";
    public static final String DEFAULT_RING_QUEUETIP_VOICEBOX = "queuetip_voicebox.wav";
	public static final String DEFAULT_RING_QUEUE_KEY_ERROR = "queue_key_error.wav";
	public static final String QUEUE_NUM_PREFIX = "queue_num_";
	public static final String DEFAULT_RING_QUEUE_1 = "queue_1.wav";
	public static final String DEFAULT_RING_QUEUE_3 = "queue_3.wav";
    
	public static final String DEFAULT_RING_QUEUE_4 = "queue_4.wav";
	public static final String DEFAULT_RING_QUEUE_4_NOMESSAGE = "queue_4_nomessage.wav";
    public static final String DEFAULT_RING_QUEUE_4_OFFLINE = "queue_4_offline.wav";
	public static final String DEFAULT_RING_QUEUE_4_OFFLINE_NOMESSAGE = "queue_4_offline_nomessage.wav";
    
    public static final String DEFAULT_RING_SUPPORT = "support.wav";
    public static final String DEFAULT_RING_OFFLINE_TIP = "offline_tip.wav";
    public static final String DEFAULT_RING_TRANS_ANGET_TIP = "transfer_agent_tip.wav";
    public static final String DEFAULT_RING_TRANS_CALLER_TIP = "transfer_caller_tip.wav";
    
    public static final String DEFAULT_RING_VALID_FAIL = "valid_fail.wav";
    public static final String DEFAULT_RING_VALID_INPUT = "valid_input.wav";
    public static final String DEFAULT_RING_VALID_SUCCESS = "valid_success.wav";
    public static final String DEFAULT_RING_VALID_CHECKED = "valid_checked.wav";
    public static final String DEFAULT_RING_VALID_ERROR = "valid_error.wav";
    
    public static final String DEFAULT_RING_AUTO_NOTIFY_OPEN = "auto_notify_open.wav";
    public static final String DEFAULT_RING_AUTO_NOTIFY_RING = "auto_notify_ring.wav";
    public static final String DEFAULT_RING_TRANS_TO_IVR = "trans_to_ivr.wav";
    public static final String DEFAULT_RING_TRANS_TO_IVR_FAIL = "trans_to_ivr_fail.wav";
    
    public static final String WHITE_LIST_NOT_IN = "whiteListNotIn.wav";
    public static final String WHITE_LIST_COUNT_LIMIT = "whiteListCountLimit.wav";
    public static final String SPEECH_ERROR_TIP = "speechErrorTip.wav";
    
    public static final String MISS_CALL_TIP = "MISS_CALL_TIP.wav";
    
	//坐席状态
    public static final String AGENT_STATE_IDLE = "idle";
    public static final String AGENT_STATE_CONNECT = "connect";
    public static final String AGENT_STATE_BUSY = "busy";
    public static final String AGENT_STATE_CALLING = "calling";
    public static final String AGENT_STATE_OFFLINE = "offline";
    public static final String AGENT_STATE_ORDER = "order";//整理状态

	//队列状态
    public static final int QUEUE_STATE_QUEUING = 0;
    public static final int QUEUE_STATE_INCOME = 1;
    public static final int QUEUE_STATE_TIMOUT = 2;
    public static final int QUEUE_STATE_FULL = 3;
    public static final int QUEUE_STATE_OFF = 4;
    public static final int QUEUE_STATE_OVER = 5;
    public static final int QUEUE_STATE_LEAVEQUEUE = -1;
    public static final int QUEUE_STATE_OFFLINE = 6;

	//话单类型
    public static final int TALK_TYPE_NOT_CON = 1;
    public static final int TALK_TYPE_IVR_NOT_CON = 2;
    public static final int TALK_TYPE_CON = 3;
    public static final int TALK_TYPE_IVR_CON = 4;
    public static final int TALK_TYPE_VOICE_BOX = 5;
    
    public static final String SIP_PHONE_PREFIX = "S";
    
    
    public static final int CALL_RESULT_SUCCESS = 1;
    public static final int CALL_RESULT_FAIL = 2;
    public static final int CALL_RESULT_TO_QUEUE = 0;
}
