/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.netphoneLocal.business.po.OutDataTask;
import com.sungoin.netphoneLocal.business.po.OutDataType;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.util.CtiDes;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 *
 * <AUTHOR>
 */
public class NoChargeProcessThread extends Thread {
    private static final Logger log = LoggerFactory.getLogger(NoChargeProcessThread.class);
    private final User user;
    private final ProcessService ps;
    private final Call call;
    
    public NoChargeProcessThread(User user, ProcessService ps, Call call) {
        this.user = user;
        this.ps = ps;
        this.call = call;
    }
    
    @Override
    public void run() {
        try {
            MDC.put(Constants.TRACE_ID, call.getTraceId());
            call.setParam(MidwareConstants.CALL_TYPE, "SecondSale");
            if(ps.isSecondSaleOffhook()) {
                call.answer();
            } else {
                call.alert();
            }
            Thread.sleep(200);
            String ring = ps.getDefaultRing("no_charge_" + user.getNumber() + ".wav");
            log.info("开始播放不扣费彩铃：{}", ring);
            call.play(ring, false, false, false, 60, 0);
            if(call.getState() != Constants.CALL_IDLE) {
                call.onHook();
            }
            String talkNoteId = saveTalkNote();
            log.info("未接不扣费话单记录成功！话单ID：" + talkNoteId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            MDC.remove(Constants.TRACE_ID);
        }
    }
    
    private String saveTalkNote() {
		TalkNote talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), getTalkNoteCaller(), new Date());
        //处理主叫号码，补上区号
		talkNote.setTalkType(1);
        talkNote.setEndFlag(true);
		ps.getBusinessService().saveOrUpdate(talkNote);
		ps.getBusinessService().saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.TALK));
		return talkNote.getId();
	}
    
    private String getTalkNoteCaller() {
        String caller = this.call.getCaller();
        if (caller.startsWith("1") && caller.length() == 11) {
            //mobile,not change
        } else if (caller.startsWith("01") && caller.charAt(2) != '0') {
            //0+mobile, substring(1)
            caller = caller.substring(1);
        } else {
            //固话
            caller = this.ps.getFixedCallerNo(caller);
        }
        try {
            caller = CtiDes.getInstance().encrypt(caller);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return caller;
    }
}
