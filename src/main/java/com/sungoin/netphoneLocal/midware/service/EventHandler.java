/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.cti.client.api.Call;

/**
 *
 * <AUTHOR>
 */
public interface EventHandler {
    /**
     * 主叫挂机
     */
    void callerOnhook();
    /**
     * 坐席挂机
     */
    default void calleeOnhook() {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 转接坐席挂机
     * @param trans 
     */
    default void transOnhook(Call trans) {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 外呼结束
     * @param status
     * @param errorCode 
     */
    default void callOver(int status, int errorCode) {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 按键收码
     * @param key 
     */
    default void ivrReceived(String key) {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 按键转工号
     */
    default void dtmfTrans() {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 录音CDR（sip用）
     * @param record 
     */
    default void cdr(String record) {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 语音识别（sip用）
     * @param text 
     */
    default void detectSpeech(String text) {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 按键转导航
     */
    default void dtmfToIvr() {
        throw new UnsupportedOperationException("Not supported yet.");
    }
    /**
     * 智能坐席挂机
     */
    default void robotOnhook() {
        throw new UnsupportedOperationException("Not supported yet.");
    }
}
