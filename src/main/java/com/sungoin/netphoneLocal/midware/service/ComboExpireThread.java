/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.netphoneLocal.business.po.OutDataTask;
import com.sungoin.netphoneLocal.business.po.OutDataType;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 *
 * <AUTHOR>
 */
public class ComboExpireThread extends Thread {
    private static final Logger log = LoggerFactory.getLogger(ComboExpireThread.class);
    
    private final User user;
    private final ProcessService ps;
    private final Call call;

    public ComboExpireThread(User user, ProcessService ps, Call call) {
        this.user = user;
        this.ps = ps;
        this.call = call;
    }

    @Override
    public void run() {
        try {
            MDC.put(Constants.TRACE_ID, call.getTraceId());
            call.setParam(MidwareConstants.CALL_TYPE, "ComboExpire");
            if(ps.isSecondSaleOffhook()) {
                call.answer();
            } else {
                call.alert();
            }
            Thread.sleep(200);
            String ring = ps.getDefaultRing("comboExpire.wav");
            call.play(ring, false, false, false, 10, 0);
            if(call.getState() != Constants.CALL_IDLE) {
                call.onHook();
            }
            String talkNoteId = saveTalkNote();
            log.info("套餐到期话单记录成功！话单ID：" + talkNoteId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            MDC.remove(Constants.TRACE_ID);
        }
    }
    
    private String saveTalkNote() {
		TalkNote talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), call.getCaller(), new Date());
		talkNote.setTalkType(6);
        talkNote.setEndFlag(true);
		ps.getBusinessService().saveOrUpdate(talkNote);
		ps.getBusinessService().saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.TALK));
		return talkNote.getId();
	}
}
