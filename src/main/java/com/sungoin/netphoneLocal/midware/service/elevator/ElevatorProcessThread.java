/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.elevator;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.mq.SmsSendDto;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.VoiceRecord;
import com.sungoin.netphoneLocal.constants.CallOutErrorCode;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.AGENT_STATE_CALLING;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.AGENT_STATE_IDLE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_AGENT_ID;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_BINDPHONE_ID;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_BINDPHONE_LSH;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_CALLER;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.MAIN_CALL_THREAD;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.TALK_TYPE_IVR_CON;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.TALK_TYPE_IVR_NOT_CON;
import com.sungoin.netphoneLocal.midware.exception.MidwareException;
import com.sungoin.netphoneLocal.midware.service.EventHandler;
import com.sungoin.netphoneLocal.util.CtiDes;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.MyStringUtil;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 *
 * <AUTHOR>
 */
public class ElevatorProcessThread extends TeleThread implements EventHandler {

    private static final Logger log = LoggerFactory.getLogger(ElevatorProcessThread.class);
    private transient ElevatorProcessService service;//序列化时忽略此域，反序列化时手动赋值

    private final String logPrefix;
    private final NumberFuncConfig functionConfig;
    private final User user;
    private volatile Dept currentDept;
    private volatile Call mainCall;
    private volatile Call agent;
    private volatile BindPhone currentBindPhone;
    private volatile TalkNote talkNote;
    private volatile VoiceRecord voiceRecord;
    private String fixedCallerNo;//处理过的主叫号码
    private String fixedCalleeNo;//处理过的被叫号码
    private volatile int talkType = TALK_TYPE_IVR_NOT_CON;

    //异步呼叫控制
    private final String makeCallSync = new String();
    private volatile boolean isMakingCall = false;
    private volatile boolean isCallOver = false;
    private boolean isCallSuccess = false;
    private boolean isRejectCall = false;
    private volatile boolean isCallConnected = false;//是否呼通的标识
    private volatile int callErrorCode = -1;
    
    //主叫挂机标识
    private AtomicBoolean callerOnhooked = new AtomicBoolean(false);

    //IVR导航异步收码控制
    private final String ivrSync = new String();
    private boolean isIvrOver;
    private StringBuffer dtmf;

    private ElevatorUserInfo userInfo;
    private String code;
    private String currentPhone;

    public static final String CALLER_MSG = "【电梯云服务】您好，请耐心等待专业救援人员前来救援。轿厢不是密闭空间，没有窒息危险，千万不要强行扒门或打开顶部安全窗错误自救。想了解更多电梯安全知识，请关注“电梯云服务”公众号。";
    public static final String CALLEE_MSG1 = "【电梯云服务】您好，电梯编号为%s的电梯发生困人事件，请使用“电梯云服务”微信小程序进行调度！困人电梯：%s ；报警时间：%s ；被困人电话：%s。";
    public static final String CALLEE_MSG2 = "【电梯云服务】您好，电梯编号为%s的电梯发生困人事件，请使用“电梯云服务”微信小程序确认救援，并立即赶赴现场实施救援！困人电梯：%s ；报警时间：%s ；被困人电话：%s。救援后，请及时提交救援单。";

    /**
     * 反序列化时给transient域service赋值
     *
     * @param in
     * @throws IOException
     * @throws ClassNotFoundException
     */
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
        this.service = SpringHelper.getBean(ElevatorProcessService.class);
    }

    public ElevatorProcessThread(Call mainCall, User user, TeleThreadEventListener listener, NumberFuncConfig config) {
        super(listener);
        Date now = new Date();
        this.user = user;
        this.mainCall = mainCall;
        this.logPrefix = mainCall.getCaller() + "-" + user.getNumber() + "-" + DateTimeUtil.formatTime(now);
        this.talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), null, now);
        this.functionConfig = config;
        this.init();
    }

    private void init() {
        this.service = SpringHelper.getBean(ElevatorProcessService.class);
        this.mainCall.attachTeleThread(this);
        this.mainCall.setParam(CALL_TYPE, CALL_TYPE_CALLER);
        this.mainCall.setParam(MAIN_CALL_THREAD, this);
    }

    private void debug(String string, Object... os) {
        log.debug(this.logPrefix + string, os);
    }

    private void info(String string, Object... os) {
        log.info(this.logPrefix + string, os);
    }

    private void warn(String string, Object... os) {
        log.warn(this.logPrefix + string, os);
    }

    private void error(String string, Object... os) {
        log.error(this.logPrefix + string, os);
    }

    private void errorStackTrace(String string, Throwable throwable) {
        log.error(this.logPrefix + string, throwable);
    }

    public String getLogPrefix() {
        return this.logPrefix;
    }

    @Override
    public void run() {
        try {
            MDC.put(Constants.TRACE_ID, mainCall.getTraceId());
            this.debug("开始电梯业务流程！");
            int ret = this.answerOrAlert();
            if (this.user.isPreOffhook()) {
                //如果在摘机前，主叫已经挂机了，防止摘机时间晚于挂机时间，直接取挂机时间
                Date offHookTime = this.talkNote.getOnhookTime() == null ? new Date() : this.talkNote.getOnhookTime();
                this.talkNote.setCallerOffhookTime(offHookTime);
            }
            //处理主叫号码，补上区号
            this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
            this.debug("处理过后的主叫号码：{}", this.fixedCallerNo);
            this.talkNote.setCallerNo(this.getTalkNoteCaller());
            this.service.saveOrUpdateTalkNote(this.talkNote, false);
            this.debug("保存话单成功！");
            if (ret != Constants.RET_SUCCESS) {
                throw new MidwareException(this.logPrefix + "对主叫：" + this.mainCall.toString() + "摘机失败！");
            }
            this.currentDept = this.service.getRootDept(this.user);
            //处理ivr收码
            this.processIvr();
            if(NumberUtils.isNumber(this.code)) {
                this.talkNote.setGh(Long.parseLong(this.code));
            }
            this.mainCall.stopPlay();
            this.debug("接口获取到的救援人员信息：{}", userInfo);
            if (this.userInfo != null && !userInfo.getCalleeList().isEmpty()) {
                //发送坐席短信
                this.sendUserMessage();
                String callRing = this.service.getCustomRing("elevator_call.wav");
                this.debug("播放救援接听彩铃：{}", callRing);
                this.mainCall.play(callRing, true, false, true, 0, 0);
                this.processMakeCall(userInfo.getCalleeList());
            } else {
                //呼叫备用救援
                this.debug("未获取到救援人员信息，使用备用电话！");
                List<BindPhone> agents = this.service.getIdleAgentsByDept(this.currentDept, this.fixedCallerNo);
                this.debug("获取到空闲的备用号码：{}", agents);
                if (!agents.isEmpty()) {
                    String standbyRing = this.service.getCustomRing("elevator_standby.wav");
                    this.debug("播放备用救援接听彩铃：{}", standbyRing);
                    this.mainCall.play(standbyRing, true, false, true, 0, 0);
                    this.processMakeCallWithBindPhone(agents);
                }
            }
            if (this.agent != null) {
                initCallParams(this.agent, MidwareConstants.CALL_TYPE_CALLEE);
                this.calleeOffHook();
                this.processRecord();
                boolean connected = this.connectCall(this.mainCall, this.agent);
                if (!connected) {
                    this.warn("连接主被叫失败！当前主叫状态：{}， 当前被叫状态：{}", getCallStateDesc(this.mainCall.getState()), getCallStateDesc(this.agent.getState()));
                    throw new MidwareException("连接主被叫失败！");
                }
                this.isCallConnected = true;
                if (this.currentBindPhone != null) {
                    this.service.midwareUpdateAgentState(this.currentBindPhone, MidwareConstants.AGENT_STATE_CONNECT);
                }
                //发送主叫短信
                this.sendCallerMessage();
            } else {
                String failRing = this.service.getCustomRing("elevator_fail.wav");
                this.debug("呼叫失败，播放失败提示音：{}", failRing);
                this.mainCall.play(failRing, false, false, false, 15, 0);
                if (this.mainCall.getState() != Constants.CALL_IDLE) {
                    this.mainCall.onHook();
                }
            }
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            this.warn("程序异常，挂断主被叫");
            hangupBothCall();
        } finally {
            this.debug("主线程结束！");
            this.finished();
        }
    }

    private void sendCallerMessage() {
        SmsSendDto dto = new SmsSendDto();
        dto.setType(5);
        dto.setCallerNo(this.mainCall.getCaller());
        dto.setNumberCode(this.user.getNumber());
        dto.setDeptId(this.currentDept.getDeptLsh());
        dto.setMsg(CALLER_MSG);
        this.service.sendSmsMessage(dto);
        this.debug("发送主叫短信：号码：{}，内容：{}", dto.getCallerNo(), dto.getMsg());
    }

    private void sendUserMessage(String phone, String msg) {
        SmsSendDto dto = new SmsSendDto();
        dto.setType(5);
        dto.setCallerNo(phone);
        dto.setNumberCode(this.user.getNumber());
        dto.setDeptId(this.currentDept.getDeptLsh());
        dto.setMsg(msg);
        this.service.sendSmsMessage(dto);
        this.debug("发送坐席短信：号码：{}，内容：{}", dto.getCallerNo(), dto.getMsg());
    }

    private void sendUserMessage() {
        String phone1 = this.userInfo.getSos_s_phone_one();
        String phone2 = this.userInfo.getSos_m_phone_one();
        String phone3 = this.userInfo.getSos_m_phone_two();
        String phone4 = this.userInfo.getSos_s_phone_two();
        String time = DateTimeUtil.format(this.talkNote.getIncomingTime(), DateTimeUtil.Pattern.TIME);
        String msg1 = String.format(CALLEE_MSG1, this.code, this.userInfo.getProject_name() + this.userInfo.getNumber_info(), time, this.mainCall.getCaller());
        String msg2 = String.format(CALLEE_MSG2, this.code, this.userInfo.getProject_name() + this.userInfo.getNumber_info(), time, this.mainCall.getCaller());
        if (MyStringUtil.isMobile(phone1)) {
            sendUserMessage(phone1, msg1);
        }
        if (MyStringUtil.isMobile(phone4)) {
            sendUserMessage(phone4, msg1);
        }
        if (MyStringUtil.isMobile(phone2)) {
            sendUserMessage(phone2, msg2);
        }
        if (MyStringUtil.isMobile(phone3)) {
            sendUserMessage(phone3, msg2);
        }
    }

    private void processIvr() {
        boolean receiveFinished = false;
        int errorCount = 0;
        String inputRing = this.service.getCustomRing("elevator_input.wav");
        this.debug("开始播放输入电梯号码提示音：{}", inputRing);
        this.mainCall.play(inputRing, true, false, true, 0, 0);
        int timeoutSecond = currentDept.getIvrStayTime() != null ? currentDept.getIvrStayTime() + 1 : this.service.getIvrTimeoutSeconds();
        this.debug("按键收码超时时长：{}", timeoutSecond);
        if (this.functionConfig != null && this.functionConfig.getInputTimeOut() != null) {
            timeoutSecond = functionConfig.getInputTimeOut();
            this.debug("号码配置了按键收码等待时长：{}", timeoutSecond);
        }
        String ivr = this.receiveIvrDtmf(7, timeoutSecond);
        this.debug("收到用户的按键：{}", ivr);
        receiveFinished = isIvrFinished(ivr);
        while (++errorCount< 10 && !receiveFinished) {
            if (this.mainCall.getState() == Constants.CALL_IDLE) {
                throw new MidwareException("主叫已挂机！");
            }
            this.mainCall.stopPlay();
            this.await(200);
            String errorTIp = this.service.getCustomRing("elevator_input_error.wav");
            this.debug("开始播放按键错误提示音：{}", errorTIp);
            this.mainCall.play(errorTIp, true, false, true, 0, 0);
            ivr = this.receiveIvrDtmf(7, timeoutSecond);
            receiveFinished = isIvrFinished(ivr);
        }
        this.debug("退出IVR收码，errorCount = {}, receiveFinished = {}", errorCount, receiveFinished);
        if(errorCount >= 10) {
            this.callErrorCode = CallOutErrorCode.FORBID;
            if(this.mainCall.getState() == Constants.CALL_CONTENT) {
                String failRing = this.service.getCustomRing("elevator_fail.wav");
                this.debug("超过10次错误，播放失败提示音：{}", failRing);
                this.mainCall.play(failRing, false, false, false, 15, 0);
            }
            throw new MidwareException("用户按键错误超过10次，终止流程！");
        }
    }

    private boolean isIvrFinished(String ivr) {
        if(ivr.length() == 7 && ivr.endsWith("#") && NumberUtils.isDigits(ivr.substring(0, 6))) {
            this.code = ivr.substring(0, 6);
            try {
                this.userInfo = service.getUserInfo(this.code);
                this.debug("根据输入的号码：{}，获取到用户信息：{}", this.code, this.userInfo);
            } catch (Exception e) {
                this.warn("获取用户信息异常！退出ivr流程");
                return true;
            }
            return this.userInfo != null;
        } else {
            return false;
        }
    }

    private void initCallParams(Call callee, String callType) {
        callee.attachTeleThread(this);
        callee.setParam(CALL_TYPE, callType);
        if (this.currentBindPhone != null) {
            callee.setParam(CALL_BINDPHONE_ID, this.currentBindPhone.getId());
            callee.setParam(CALL_BINDPHONE_LSH, this.currentBindPhone.getCustomerNo() + "_" + this.currentBindPhone.getLsh());
            callee.setParam(CALL_AGENT_ID, this.currentBindPhone.getAgentId());
        }
    }

    private void processMakeCall(List<String> list) {
        for (String phone : list) {
            this.currentPhone = phone;
            Call call = this.makeCall(phone);
            if (call != null) {
                this.agent = call;
                break;
            }
        }
    }

    private void processMakeCallWithBindPhone(List<BindPhone> bindList) {
        for (BindPhone phone : bindList) {
            this.currentBindPhone = this.service.getLocalBindPhone(phone.getCustomerNo(), phone.getLsh());;
            this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_CALLING + "_" + this.service.isMainServer());
            Call call = this.makeCall(this.currentBindPhone.getOrigBindPhoneNo());
            if (call != null) {
                this.agent = call;
                break;
            } else {
                //如果状态任然是呼叫中，则改回空闲
                if (this.service.getAgentStateByAgentId(this.currentBindPhone.getAgentId()).equals(
                        AGENT_STATE_CALLING + "_" + this.service.isMainServer())) {
                    this.debug("呼叫失败，绑定号码流水号：{}还原状态到空闲：", this.currentBindPhone.getLsh());
                    this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE);
                }
            }
        }
    }

    private boolean connectCall(Call caller, Call callee) {
        if (caller.getState() == Constants.CALL_IDLE || callee.getState() == Constants.CALL_IDLE) {
            this.warn("主被叫有一方挂机！退出连接！");
            return false;
        }
        int ret = caller.connectCall(callee, false);
        if (ret != Constants.RET_SUCCESS) {
            this.warn("连接主被叫失败！主叫状态：{}，被叫状态：{}，等待一秒后重新尝试一次", this.getCallStateDesc(caller.getState()),
                    this.getCallStateDesc(callee.getState()));
            this.await(1000);
            ret = caller.connectCall(callee, false);
            if (ret != Constants.RET_SUCCESS) {
                if (caller.getState() == Constants.CALL_ALERT && callee.getState() == Constants.CALL_CONTENT) {
                    this.await(1000);
                    ret = caller.connectCall(callee, false);
                }
                if (ret != Constants.RET_SUCCESS) {
                    this.error("重新连接主被叫失败！主叫状态：{}，被叫状态：{}", this.getCallStateDesc(caller.getState()),
                            this.getCallStateDesc(callee.getState()));
                }
            }
        }
        return ret == Constants.RET_SUCCESS;
    }

    private void processRecord() {
        this.mainCall.stopPlay();
        this.await(200);
        this.voiceRecord = this.service.startRecord(this.user, this.talkNote.getCallerNo(), this.fixedCalleeNo);
        this.voiceRecord.setTalkNoteId(this.talkNote.getId());
        this.service.saveOrUpdateRecord(this.voiceRecord);
        String recordPath = this.voiceRecord.getFilePath();
        this.debug("用户的录音路径：{}", recordPath);
        String[] records = recordPath.split(",");
        this.mainCall.record(records[0], 0, false);
        this.agent.record(records[1], 0, false);
    }

    private Call makeCall(String callee) {
        Call call = null;
        try {
            if (this.mainCall.getState() == Constants.CALL_IDLE) {
                throw new MidwareException("主叫已挂机！");
            }
            this.fixedCalleeNo = this.service.getFixedCalleeNo(callee);
            log.debug("处理过后的被叫号码：{}", this.fixedCalleeNo);
            CallNumDto callNumDto = new CallNumDto();
            callNumDto.setCallerNo(this.service.isOrigCalleePrefix() ? this.service.getlocalNo() + this.user.getOriginalNo() : this.user.getOriginalNo());
            callNumDto.setCalleeNo(this.fixedCalleeNo);
            long begin = System.currentTimeMillis();
            this.debug("开始呼叫坐席：主叫号码：{},被叫号码：{},开始时间：{},超时时间：{}", callNumDto.getCallerNo(), callNumDto.getCalleeNo(), begin, 25);
            call = this.asyncMakeCall(callNumDto, 25);
            long end = System.currentTimeMillis();
            long callTime = (end - begin) / 1000;
            this.debug("呼叫结束，呼叫时间：{}，呼叫结果：{}", callTime, this.isCallSuccess);
            if (this.callErrorCode == 129) {
                this.warn("发生同抢，再次重新呼叫一次！", callTime);
                begin = System.currentTimeMillis();
                call = this.asyncMakeCall(callNumDto, 25);
                end = System.currentTimeMillis();
                callTime = (end - begin) / 1000;
                this.debug("呼叫结束，呼叫时间：{}，呼叫结果：{}", callTime, this.isCallSuccess);
            }
            if (call != null && callTime < 1) {
                //呼叫成功小于1秒，则认为超时终止失败，接通的是上一个坐席
                this.warn("发生并发接通问题，接通被叫：{}，外呼被叫：{}，忽略本次接通！", call.getCallee(), callNumDto.getCalleeNo());
                call.onHook();
                call = null;
            }
        } catch (Exception e) {
            this.error(e.getMessage());
        }
        return call;
    }

    private Call asyncMakeCall(CallNumDto dto, long timeoutSeconds) {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            this.error("主叫已挂机！");
            return null;
        }
        Call call = null;
        try {
            synchronized (this.makeCallSync) {
                this.isMakingCall = true;
                this.isCallOver = false;
                this.isCallSuccess = false;
                this.isRejectCall = false;
                if (this.mainCall.getState() == Constants.CALL_IDLE) {
                    this.error("主叫已挂机！");
                    return null;
                }
                call = this.manager.makeCall(dto.getCallerNo(), dto.getCalleeNo(), dto.getOriginalNo(), true, timeoutSeconds,
                        this.getName());
                if (call == null) {
                    return call;
                }
                call.setTraceId(this.mainCall.getTraceId());
                call.setParam(MAIN_CALL_THREAD, this);
                long begin = System.currentTimeMillis();
                while (!this.isCallOver) {
                    this.makeCallSync.wait(timeoutSeconds * 1000);
                    this.info("主线程被唤醒...isCallOver={}, isCallSuccess={}", this.isCallOver, this.isCallSuccess);
                    if (this.isRejectCall
                            || (!this.isCallOver && System.currentTimeMillis() - begin >= timeoutSeconds * 1000)) {
                        this.warn((this.isRejectCall ? "用户拒接" : "超时") + "，终止呼叫！");
                        this.manager.stopMakeCall(call.getDeviceId(), call.getLsh());
                        this.isCallSuccess = false;
                        this.isCallOver = true;
                    }
                }
                this.isMakingCall = false;
            }
            return this.isCallSuccess ? call : null;
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }

    private String receiveIvrDtmf(int length, int timeoutSecond) {
        try {
            this.dtmf = new StringBuffer();
            this.mainCall.receiveDTMFAsync();
            synchronized (this.ivrSync) {
                this.isIvrOver = false;
                while (!this.isIvrOver) {
                    this.ivrSync.wait(timeoutSecond * 1000);
                    if (!this.isIvrOver) {
                        if (this.mainCall.getState() != Constants.CALL_CONTENT) {
                            this.warn("收码异常！主叫挂机！");
                            return null;
                        }
                        this.warn("ivr 收码超时！");
                        this.mainCall.stopReceiveDTMF();
                        return "";
                    }
                }
            }
            this.mainCall.stopReceiveDTMF();
            return this.dtmf.toString();
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * 保存话单中的主叫号码 规则1.如果是手机，只保存11位真正手机号。 规则2.如果是固话，保存区号+固话
     *
     * @return
     */
    private String getTalkNoteCaller() {
        String caller = this.mainCall.getCaller();
        if (caller.startsWith("1") && caller.length() == 11) {
            //mobile,not change
        } else if (caller.startsWith("01") && caller.charAt(2) != '0') {
            //0+mobile, substring(1)
            caller = caller.substring(1);
        } else {
            //固话
            caller = this.fixedCallerNo;
        }
        try {
            caller = CtiDes.getInstance().encrypt(caller);
        } catch (Exception ex) {
            this.error(ex.getMessage(), ex);
        }
        return caller;
    }

    private int answerOrAlert() {
        this.debug("用户开通了预摘机功能！对来电摘机！");
        this.mainCall.answerSync(1000);
        this.debug("对来电摘机！来电状态为：" + this.getCallStateDesc(this.mainCall.getState()));
        return this.mainCall.getState() == Constants.CALL_CONTENT ? 1 : 0;
    }

    private String getCallStateDesc(int callState) {
        switch (callState) {
            case 0:
                return "空闲";
            case 1:
                return "呼叫中";
            case 2:
                return "振铃";
            case 3:
                return "通话中";
            default:
                return "其他";
        }
    }

    public void hangupBothCall() {
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.info("挂断主叫");
            this.mainCall.onHook();
        }
        handupAgent();
    }

    public void handupAgent() {
        if (this.isMakingCall) {
            this.debug("终止呼叫坐席！");
            synchronized (this.makeCallSync) {
                this.isCallOver = true;
                this.isCallSuccess = false;
                this.isRejectCall = true;
                this.makeCallSync.notifyAll();
            }
        } else {
            if (this.agent != null && this.agent.getState() == Constants.CALL_CONTENT) {
                this.debug("挂断坐席！");
                this.agent.onHook();
            }
        }
    }

    private void calleeOffHook() {
        Date now = new Date();
        this.talkType = TALK_TYPE_IVR_CON;
        this.talkNote.setTalkType(this.talkType);
        this.talkNote.setCalleeNo(this.currentPhone);
        this.talkNote.setCalleeOffhookTime(now);
        if (currentBindPhone != null) {
            this.talkNote.setLsh(this.currentBindPhone.getId());
//            if (this.currentBindPhone.getReportNum() != null) {
//                this.talkNote.setGh(Long.valueOf(this.currentBindPhone.getReportNum()));
//            }
        }
        this.service.saveOrUpdateTalkNote(this.talkNote);
        this.debug("被叫摘机，被叫号码：{},摘机时间：{}", this.fixedCalleeNo, this.talkNote.getCalleeOffhookTime());
    }

    @Override
    public void callerOnhook() {
        if (this.callerOnhooked.compareAndSet(false, true)) {
            this.debug("主叫挂机！");
            if (this.isMakingCall) {
                this.debug("正在呼叫被叫，终止呼叫。。。");
                this.handupAgent();
            }
            synchronized (this.ivrSync) {
                this.ivrSync.notifyAll();
            }
            Date now = new Date();
            if (this.voiceRecord != null) {
                this.voiceRecord.setEndTime(now);
                this.voiceRecord.setEndFlag(true);
                this.service.endRecord(this.talkNote, this.voiceRecord, true);
            }
            if (this.talkNote.getCallerOnHook() == null) {
                this.talkNote.setCallerOnHook(true);
            }
            if (this.talkNote.getDeptId() == null) {
                this.talkNote.setDeptId(this.currentDept == null ? this.user.getNumber() : this.currentDept.getDeptLsh());
            }
            this.talkNote.setEndFlag(true);
            this.talkNote.setOnhookTime(now);
            if (!isCallConnected) {
                //未接原因
                if (this.callErrorCode < 0) {
                    this.callErrorCode = CallOutErrorCode.MAINCALL_ONHOOK;
                }
                this.talkNote.setErrorCode(callErrorCode);
            }
            if (StringUtils.isEmpty(this.talkNote.getCalleeNo()) && StringUtils.isNotEmpty(this.fixedCalleeNo)) {
                //如果未呼通，保存最后一个坐席号码
                this.talkNote.setCalleeNo(this.currentPhone);
            }
            if (this.talkNote.getCallerNo() == null) {
                this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
                this.talkNote.setCallerNo(this.getTalkNoteCaller());
            }
            this.service.endTalkNote(this.talkNote);
            this.mainCall.getAllParam().clear();
            this.handupAgent();
            //推送挂机消息
            if (this.isCallConnected) {
                OnhookCreateInfo resp = this.service.pushOnhookInfo(this.code, this.mainCall.getCaller(), DateFormatUtils.format(now, "yyyy-MM-dd HH:mm:ss"));
                this.debug("推送挂机消息返回：{}", resp);
            }
        }
    }

    @Override
    public void calleeOnhook() {
        this.debug("坐席挂机");
        this.mainCall.stopRecord();
        if (this.talkNote.getCallerOnHook() == null) {
            this.talkNote.setCallerOnHook(false);
        }
        this.agent.getAllParam().clear();
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.info("挂断主叫");
            this.mainCall.onHook();
        }
        if (this.currentBindPhone != null) {
            this.info("坐席挂机，更新坐席状态为空闲！");
            this.service.midwareUpdateAgentState(this.currentBindPhone, MidwareConstants.AGENT_STATE_IDLE);
        }
    }

    @Override
    public void callOver(int status, int errorCode) {
        this.debug("呼叫结束！呼叫结果：{}，失败原因：{}", status, errorCode);
        synchronized (this.makeCallSync) {
            if (this.isMakingCall) {
                this.isCallOver = true;
                this.isCallSuccess = (status == 1);
                this.callErrorCode = errorCode;
                this.makeCallSync.notifyAll();
                this.debug("唤醒等待线程！");
            }
        }
    }

    @Override
    public void ivrReceived(String key) {
        this.debug("收到用户按键：{}", key);
        this.dtmf.append(key);
        synchronized (this.ivrSync) {
            if (!this.isIvrOver && (key.equals("#") || dtmf.length() == 7)) {
                this.info("用户按#号键或收码满7位！退出同步块！");
                this.isIvrOver = true;
                this.ivrSync.notifyAll();
            }
        }
    }
    
    @Override
    public void cdr(String record) {
        this.debug("收到cdr事件，录音地址：{}", record);
        if (this.voiceRecord != null) {
            this.voiceRecord.setFilePath(record);
            this.voiceRecord.setFileName(record.substring(record.lastIndexOf("/") + 1));
            this.service.saveOrUpdateRecord(voiceRecord);
        }
    }
}
