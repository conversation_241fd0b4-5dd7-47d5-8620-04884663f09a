/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.mbean;

import com.sungoin.netphoneLocal.business.service.BusinessService;
import javax.annotation.Resource;
import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedOperationParameter;
import org.springframework.jmx.export.annotation.ManagedOperationParameters;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
@ManagedResource(objectName = "netphoneMBeans:name=PbxControlMbean", description = "pbx Managed Bean")
public class PbxControlMbean {

    @Resource
    private BusinessService bs;

    @ManagedOperation(description = "get pbx count")
    @ManagedOperationParameters({
    @ManagedOperationParameter(name = "bindPhoneId", description = "bindPhoneId")})
    public int getPbxCount(String bindPhoneId) {
        return bs.getConnectCount(bindPhoneId);
    }

    @ManagedOperation(description = "set pbx count")
    @ManagedOperationParameters({
    @ManagedOperationParameter(name = "bindPhoneId", description = "bindPhoneId"),
    @ManagedOperationParameter(name = "count", description = "count")
    })
    public void setPbxCount(String bindPhoneId, int count) {
        int currentCount = bs.getConnectCount(bindPhoneId);
        if (currentCount == 0) {
            bs.putConnect(bindPhoneId);
        }
        int c = count - currentCount;
        boolean asc = true;
        if(c < 0) {
            c = -c;
            asc = false;
        }
        for (int i = 0; i < c; i++) {
            if(asc) {
                bs.addConnect(bindPhoneId);
            } else {
                bs.subtractConnect(bindPhoneId);
            }
            
        }
    }
}
