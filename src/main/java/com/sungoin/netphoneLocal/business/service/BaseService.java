/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.service;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.sungoin.netphoneLocal.business.dao.*;
import com.sungoin.netphoneLocal.business.po.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sungoin.cloudstorage.client.CloudStorageClient;
import com.sungoin.cloudstorage.dto.DownLoadResponse;
import com.sungoin.netphoneLocal.business.bean.AgentStatusEnum;
import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import com.sungoin.netphoneLocal.business.bean.TalkNoteDto;
import com.sungoin.netphoneLocal.business.bean.WeekEnum;
import com.sungoin.netphoneLocal.business.rmi.DynamicRmiRegister;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.midware.controller.BaseConfigDto;
import com.sungoin.netphoneLocal.util.CacheUtil;
import com.sungoin.netphoneLocal.util.DES;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.DateTimeUtil.Pattern;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.MyStringUtil;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.util.Arrays;

import java.util.Calendar;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;

/**
 * <AUTHOR> 2015-7-16
 */
@Service
public class BaseService {

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseService.class);

    @Resource
    private UserRepository userDao;

    @Resource
    private DeptRepository deptDao;

    @Resource
    private ColorRingRepository colorRingDao;

    @Resource
    private CrbtRingRepository crbtRingDao;

    @Resource
    private BindPhoneRepository bindPhoneDao;

    @Resource
    private WhiteListRepository whiteListDao;

    @Resource
    private BlackListRepository blackListDao;

    @Resource
    private MobileLocationRepository mobileLocationDao;

    @Resource
    private DeptTimePlanRepository deptTimePlanDao;

    @Resource
    private PhoneTimePlanRepository phoneTimePlanDao;

    @Resource
    private LocationSettingRepository locationSettingDao;

    @Resource
    private InterConnectionRepository InterConnectionDao;
    
    @Resource
    private PbxInfoRepository pbxInfoDao;

    @Resource
    private CommonSettings commonSettings;

    @Resource
    private MidwareSettings midwareSettings;

    @Resource
    private JdbcTemplate jdbcTemplate;
    
    @Resource
    private SecondSaleNoRepository secondSaleDao;
    
    @Resource
    private TemplateService templateService;

    @Resource
    private RobotAgentConfigDao robotAgentConfigDao;
    
    private static final String CALLEE_PREFIX = "0";

    private static final String ALL_MODLE = "1";
    private static final int ALL_MODLE_INT = 1;

    private static final Map<String, String> districtMap = new HashMap<String, String>();

    public User saveUser(User user) {
        //如果彩铃和导航都关闭，清空根部门铃声
        if (user.isIvrFlag() == false && user.isColorringFlag() == false && !user.getDepts().isEmpty()) {
            Dept dept = user.getRootDept();
            dept.setColorRing(null);
            dept.setColorRingLsh(null);
            this.deptDao.save(dept);
        }
        //关闭炫铃，清空号码炫铃数据
        if (!user.isFlashringFlag()) {
            LOGGER.debug("{}:关闭炫铃，清空炫铃数据", user.getNumber());
            this.cleanCrbtRing(user.getNumber());
        }
        return this.userDao.save(user);
    }

    public User getUser(String id) {
        return this.userDao.findOne(id);
    }

    public Long findDeptCountByCustomerNo(String customerNo) {
        return this.deptDao.findDeptCountByCustomerNo(customerNo);
    }

    public Long findDeptCountByCustomerNoAndParentDeptLsh(String customerNo, String parentDeptLsh) {
        return this.deptDao.findDeptCountByCustomerNoAndParentDeptLsh(customerNo, parentDeptLsh);
    }

	@Deprecated
    public Dept findDeptByCustomerNoAndIvrKey(String customerNo, String ivrKey) {
        return this.deptDao.findDeptByCustomerNoAndIvrKey(customerNo, ivrKey);
    }

    public User getUserByNumber(String number) {
        return this.userDao.getUserByNumber(number);
    }

    public User getUserByOriginalNo(boolean groupCall, String number, String caller) {
        if (groupCall) {
            String userNo = this.commonSettings.getLocalCustomerNo();
            LOGGER.debug("满足群呼配置：小号={},主叫={},使用配置的400号码：{}", number, caller, userNo);
            User user = this.getUserByNumber(userNo);
            LOGGER.debug("找到群呼配置的400号码：{}", user);
            if (user != null) {
                //群呼号码使用真正的小号
                user.setOriginalNo(number);
            }
            return user;
        }
        return this.userDao.getUserByOriginalNo(number);
    }
    
    public boolean isGroupCall(String number, String caller) {
        String sql = "select count(*) from base_phone_config"
            + " where phone_no= ? and no_status=? and ((caller_start is null and caller_end is null) "
            + " or  (caller_start is not null and caller_end is not null "
            + "and caller_start <= ? and caller_end >= ? ))";
        List<Object> params = new ArrayList<Object>();
        params.add(number);
        params.add(1);
        params.add(caller);
        params.add(caller);
        int count = this.jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        return count > 0;
    }
	
	public User getUserByOriginalNo(String number) {
		return this.userDao.getUserByOriginalNo(number);
	}

    public List<User> getAll() {
        return this.userDao.getAllUser();
    }

    @Transactional
    public void deleteUser(User user) {
        this.whiteListDao.removeUser(user);
        this.blackListDao.removeUser(user);
        this.deptDao.removeUser(user);
        this.userDao.delete(user);
    }

    public Dept getDept(String id) {
        return this.deptDao.findOne(id);
    }

    public Dept saveOrUpdateDept(Dept dept) {
	   if (StringUtils.isNotEmpty(dept.getColorRingLsh())) {
	      dept.setColorRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getColorRingLsh()));
	   }else{
		   dept.setColorRing(null);
	   }
	   if (StringUtils.isNotEmpty(dept.getSalesAlertToneLsh())) {
	      dept.setAgentAlertRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getSalesAlertToneLsh()));
	   }else{
		   dept.setAgentAlertRing(null);
	   }
	   if (StringUtils.isNotEmpty(dept.getSatiRingLsh())) {
	      dept.setSatiRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getSatiRingLsh()));
	   }else{
		   dept.setSatiRing(null);
	   }
	   if (StringUtils.isNotEmpty(dept.getCompanyRingLsh())) {
	      dept.setCompanyRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getCompanyRingLsh()));
	   }else{
		   dept.setCompanyRing(null);
	   }
	   if (StringUtils.isNotEmpty(dept.getResetRingLsh())) {
	      dept.setResetRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getResetRingLsh()));
	   }else{
		   dept.setResetRing(null);
	   }
       return this.deptDao.save(dept);
    }
    
    public Dept saveOrUpdateDept(Dept dept, boolean cleanCallNum) {
       if (StringUtils.isNotEmpty(dept.getColorRingLsh())) {
   	      dept.setColorRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getColorRingLsh()));
   	   }else{
   		   dept.setColorRing(null);
   	   }
   	   if (StringUtils.isNotEmpty(dept.getSalesAlertToneLsh())) {
   	      dept.setAgentAlertRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getSalesAlertToneLsh()));
   	   }else{
   		   dept.setAgentAlertRing(null);
   	   }
   	   if (StringUtils.isNotEmpty(dept.getSatiRingLsh())) {
   	      dept.setSatiRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getSatiRingLsh()));
   	   }else{
   		   dept.setSatiRing(null);
   	   }
   	   if (StringUtils.isNotEmpty(dept.getCompanyRingLsh())) {
   	      dept.setCompanyRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getCompanyRingLsh()));
   	   }else{
   		   dept.setCompanyRing(null);
   	   }
   	   if (StringUtils.isNotEmpty(dept.getResetRingLsh())) {
   	      dept.setResetRing(this.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getResetRingLsh()));
   	   }else{
   		   dept.setResetRing(null);
   	   }
        this.bindPhoneDao.cleanAgentCallNumByDeptId(dept);
        return this.deptDao.save(dept);
    }

    @Transactional
    public void cleanDept(String customerNo) {
        LOGGER.debug("cleanDept customerNo:{}", customerNo);
        List<Dept> depts = this.deptDao.findDeptByCustomerNo(customerNo);
        if (!depts.isEmpty()) {
			depts.forEach((dept) -> {
				this.deleteDept(dept);
			});
        } else {
            LOGGER.warn("cleanDept 未找到对应用户,customerNo:{}", customerNo);
        }
    }

    public List<Dept> findDeptByCustomerNo(String customerNo) {
        return this.deptDao.findDeptByCustomerNo(customerNo);
    }

    @Transactional
    public void deleteDept(Dept dept) {
        dept = this.getDept(dept.getId());
        dept.getUser().removeDept(dept);
        this.crbtRingDao.removeDept(dept);
        this.bindPhoneDao.removeDept(dept);
        this.deptDao.removeParentDept(dept);
        this.deptDao.delete(dept);
    }

    @Transactional
    public void deleteColorRing(ColorRing ring) {
        this.deptDao.removeColorRing(ring);
        this.deptDao.removeAgentAlertRing(ring);
        this.crbtRingDao.removeColorRing(ring);
        this.colorRingDao.delete(ring);
    }

    @Transactional
    public void deleteBindPhone(Dept dept) {
        LOGGER.debug("delete bindPhone dept:{}", dept);
        dept = this.getDept(dept.getId());
        for (BindPhone phone : dept.getBindList()) {
            this.phoneTimePlanDao.removeBindPhone(phone);
            this.bindPhoneDao.delete(phone);
        }
        dept.getBindList().clear();
    }

    @Transactional
    public void deleteBindPhone(BindPhone phone) {
        LOGGER.debug("delete bindPhone phone:{}", phone);
        phone = this.bindPhoneDao.findOne(phone.getId());
        this.phoneTimePlanDao.removeBindPhone(phone);
        if (phone.getDept() != null) {
            phone.getDept().removeBindPhone(phone);
        }
        this.bindPhoneDao.delete(phone);
    }

    @Deprecated
    @Transactional
    public void cleanBindPhone(String customerNo) {
        LOGGER.debug("delet cleanBindPhone customerNo:{}", customerNo);
        List<BindPhone> phoneList = this.bindPhoneDao.findBindPhoneByCustomerNo(customerNo);
        for (BindPhone phone : phoneList) {
            this.phoneTimePlanDao.removeBindPhone(phone);
            this.bindPhoneDao.delete(phone);
        }
    }

    public ColorRing findColorRingByCustomerAndLsh(String customerNo, String lsh) {
        return this.colorRingDao.findByCustomerNoAndLsh(customerNo, lsh);
    }

    public List<ColorRing> findAllColorRingByCustomerAndLsh(String customerNo, String lsh) {
        return this.colorRingDao.findAllByCustomerNoAndLsh(customerNo, lsh);
    }

    @Transactional
    public void syncUser(User user) {
        LOGGER.debug("sync User :{}", user);
        this.whiteListDao.attachUser(user, user.getNumber());
        this.blackListDao.attachUser(user, user.getNumber());
        this.deptDao.attachUser(user, user.getNumber());

    }

    @Transactional
    public void syncDept(Dept dept) {
        LOGGER.debug("sync Dept :{}", dept);
        dept = this.deptDao.findOne(dept.getId());
        //更新自己父部门
        if (StringUtils.isNotBlank(dept.getParentDeptLsh())) {
            Dept parentDept = this.deptDao.getDeptByCustomerNoAndDeptLsh(dept.getCustomerNo(), dept.getParentDeptLsh());
            if (parentDept != null) {
                dept.setParentDept(parentDept);
                //                this.deptDao.attachParentDept(parentDept, dept.getCustomerNo(),
                //                    dept.getParentDeptLsh());
            }
        }

        //更新自己子部门
        this.deptDao.attachChildrenDept(dept, dept.getCustomerNo(), dept.getDeptLsh());

        this.bindPhoneDao.attachDept(dept, dept.getCustomerNo(), dept.getDeptLsh());

        User user = this.getUserByNumber(dept.getCustomerNo());
        if (user != null) {
            this.deptDao.attachUser(user, dept.getCustomerNo());
            user.addDept(dept);
        }

    }

    @Transactional
    public void syncColorRing(ColorRing ring) {
        LOGGER.debug("sync ColorRing :{}", ring);
        //如果是彩铃，特殊处理
        //        if (ring.getCategory() == BusinessConstants.CATEGORY_RING
        //            && ring.isOnlineFlag()) {
        //            Dept dept = this.deptDao.findOne(ring.getCustomerNo());
        //            dept.setColorRing(ring);
        //        }
        List<Dept> deptList = this.deptDao.findDeptByCustomerNo(ring.getCustomerNo());
        for (Dept dept : deptList) {
            if (ring.getLsh().equals(dept.getColorRingLsh())) {
                dept.setColorRing(ring);
            }
            if (ring.getLsh().equals(dept.getSalesAlertToneLsh())) {
                dept.setAgentAlertRing(ring);
            }
            if (ring.getLsh().equals(dept.getSatiRingLsh())) {
                dept.setSatiRing(ring);
            }
            if (ring.getLsh().equals(dept.getCompanyRingLsh())) {
                dept.setCompanyRing(ring);
            }
            if (ring.getLsh().equals(dept.getResetRingLsh())) {
                dept.setResetRing(ring);
            }
        }

        List<CrbtRing> crbtRingList = this.crbtRingDao.findCrbtRingByCustomerNo(ring.getCustomerNo());
        for (CrbtRing crbtRing : crbtRingList) {
            if (ring.getLsh().equals(crbtRing.getColorRingLsh())) {
                crbtRing.setColorRing(ring);
            }
        }
        //                this.deptDao.attachColorRing(ring, ring.getCustomerNo(), ring.getLsh());
        //                this.deptDao.attachAgentAlertRing(ring, ring.getCustomerNo(),
        //                    ring.getLsh());
        //                this.crbtRingDao.attachColorRing(ring, ring.getCustomerNo(),
        //                    ring.getLsh());
    }

    @Transactional
    public void syncCrbtRing(CrbtRing crbtRing) {
        LOGGER.debug("sync CrbtRing :{}", crbtRing);
        ColorRing ring = this.findColorRingByCustomerAndLsh(crbtRing.getCustomerNo(), crbtRing.getColorRingLsh());
        if (ring != null) {
            crbtRing.setColorRing(ring);
        }

        Dept dept = this.deptDao.getDeptByCustomerNoAndDeptLsh(crbtRing.getCustomerNo(), crbtRing.getDeptLsh());
        if (dept != null) {
            dept.addCrbtRing(crbtRing);
        }
    }

    @Transactional
    public void syncWhiteList(WhiteList whiteList) {
        LOGGER.debug("sync WhiteList :{}", whiteList);

        User user = this.getUserByNumber(whiteList.getCustomerNo());
        if (user != null) {
            user.addWhiteList(whiteList);
        }
    }

    @Transactional
    public void syncBlackList(BlackList blackList) {
        LOGGER.debug("sync BlackList :{}", blackList);

        User user = this.getUserByNumber(blackList.getCustomerNo());
        if (user != null) {
            user.addBlackList(blackList);
        }
    }

    @Transactional
    public void syncBindPhone(BindPhone bindPhone) {
        LOGGER.debug("sync BindPhone :{}", bindPhone);
        this.phoneTimePlanDao.attachBindPhone(bindPhone, bindPhone.getCustomerNo(), bindPhone.getLsh());
    }

    @Transactional
    public void syncDeptTimePlan(DeptTimePlan timePlan) {
        LOGGER.debug("sync DeptTimePlan :{}", timePlan);
        this.deptTimePlanDao.save(timePlan);
    }

    @Transactional
    public void syncBindPhoneTimePlan(BindPhoneTimePlan timePlan) {
        LOGGER.debug("sync BindPhoneTimePlan :{}", timePlan);

        BindPhone phone = this.bindPhoneDao.getBindPhoneByCustomerNoAndLsh(timePlan.getCustomerNo(),
            timePlan.getBindPhoneLsh());
        if (phone != null) {
            phone.addBindPhoneTimePlan(timePlan);
        } else {
            LOGGER.debug("syncBindPhoneTimePlan未找到对应绑定号码 timePlan:{}", timePlan);
        }
        this.phoneTimePlanDao.save(timePlan);
    }

    @Transactional
    public void saveOrUpdateBindPhone(BindPhone bindPhone) {
        if (bindPhone.getDept() == null && bindPhone.getDeptLsh() != null) {
            Dept dept = this.deptDao.getDeptByCustomerNoAndDeptLsh(bindPhone.getCustomerNo(), bindPhone.getDeptLsh());
            if (dept != null) {
                dept.addBindPhone(bindPhone);
            }
            LOGGER.debug("绑定号码：{}对应的部门ID：{}", bindPhone.getId(), dept == null ? "" : dept.getId());
        }
        this.bindPhoneDao.save(bindPhone);
    }

    @Transactional
    public void saveOrUpdateColorRing(ColorRing ring) {
        this.colorRingDao.save(ring);
    }

    @Transactional
    public void saveOrUpdateCrbtRing(CrbtRing ring) {
        this.crbtRingDao.save(ring);
    }

    @Transactional
    public void saveOrUpdateWhiteList(WhiteList white) {
        this.whiteListDao.save(white);
    }

    @Transactional
    public void saveOrUpdateBlackList(BlackList black) {
        this.blackListDao.save(black);
    }

    public String getDistrictNo(String mobile) {
        LOGGER.debug("mobile :{}", mobile);
        MobileLocation location = this.mobileLocationDao.getMobileLocationByNo(MyStringUtil.getNoByMobile(mobile));
        return location != null ? location.getDistrictNo() : "";
    }

    public String getLocation(String mobile) {
        LOGGER.debug("mobile :{}", mobile);
        MobileLocation location = this.mobileLocationDao.getMobileLocationByNo(MyStringUtil.getNoByMobile(mobile));
        return location != null ? location.getLocation() : "-";
    }


    @Transactional
    public void cleanBlackList(String customerNo) {
        LOGGER.debug("cleanBlackList customerNo:{}", customerNo);
        User user = this.userDao.getUserByNumber(customerNo);
        if (user != null) {
            this.blackListDao.deleteBlackList(customerNo);
            user.getBlackList().clear();
        } else {
            LOGGER.warn("cleanBlackList 未找到对应用户,customerNo:{}", customerNo);
        }
    }

    @Transactional
    public void cleanWhiteList(String customerNo) {
        LOGGER.debug("cleanWhiteList customerNo:{}", customerNo);
        User user = this.userDao.getUserByNumber(customerNo);
        if (user != null) {
            this.whiteListDao.deleteWhiteList(customerNo);
            user.getWhiteList().clear();
        } else {
            LOGGER.warn("cleanWhiteList 未找到对应用户,customerNo:{}", customerNo);
        }
    }

    @Transactional
    public void cleanColorRing(String customerNo, int category) {
        LOGGER.debug("cleanColorRing customerNo:{}", customerNo);
        List<ColorRing> ringList = this.colorRingDao.findColorRingByCustomerNoAndCategory(customerNo, category);
        for (ColorRing ring : ringList) {
            this.deleteColorRing(ring);
        }
    }

    @Transactional
    public void cleanCrbtRing(String customerNo) {
        LOGGER.debug("cleanCrbtRing customerNo:{}", customerNo);
        List<CrbtRing> ringList = this.crbtRingDao.findCrbtRingByCustomerNo(customerNo);
        for (CrbtRing ring : ringList) {
            if (ring.getDept() != null) {
                ring.getDept().removeCrbtRing(ring);
            }
            this.crbtRingDao.delete(ring);
        }
    }

    @Transactional
    public List<BindPhone> getIdleAgent(Dept dept, String callerNo) {
        Dept localDept = this.getDept(dept.getId());
        List<BindPhone> bindPhoneList = this.bindPhoneDao.findBindPhoneByDeptAndStatus(localDept,
            AgentStatusEnum.IDLE.getValue());
        return this.getSatisfyTimePlanAgents(localDept.getUser(), bindPhoneList, callerNo);
    }

    @Transactional
    public List<BindPhone> getAllAgentsByDept(Dept dept, String callerNo) {
        Dept localDept = this.getDept(dept.getId());
        List<BindPhone> bindPhoneList = localDept.getBindList();
        return this.getSatisfyTimePlanAgents(localDept.getUser(), bindPhoneList, callerNo);
    }

    private List<BindPhone> getSatisfyTimePlanAgents(User user, List<BindPhone> bindPhoneList, String callerNo) {
        List<BindPhone> satisfyTimePlanAgents = new ArrayList<>();
        Date now = new Date();
        for (BindPhone phone : bindPhoneList) {
            //判断是否符合当前部门的地区策略
            if (!checkBindPhoneAreaModle(phone, callerNo)) {
                continue;
            }
            //判断是否符合时间策略
            if(checkBindPhoneTimeModle(user, phone, now)) {
                satisfyTimePlanAgents.add(phone);
            }
        }
        return satisfyTimePlanAgents;
    }

    @Transactional
    public void cleanDeptTimePlan(String customerNo, String deptLsh) {
        LOGGER.debug("cleanDeptTimePlan customerNo:{},deptLsh:{}", customerNo, deptLsh);
        this.deptTimePlanDao.deleteDeptTimePlan(customerNo, deptLsh);
    }

    @Transactional
    public void cleanDeptTimePlanByDept(Dept dept) {
        LOGGER.debug("cleanDeptTimePlanByDept dept:{}", dept);
        this.cleanDeptTimePlan(dept.getCustomerNo(), dept.getDeptLsh());
    }

    @Transactional
    public void cleanPhoneTimePlan(String customerNo, String lsh) {
        LOGGER.debug("cleanPhoneTimePlan customerNo:{},lsh", customerNo, lsh);
        BindPhone phone = this.bindPhoneDao.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        this.cleanPhoneTimePlanByBindPhone(phone);
    }

    @Transactional
    public void cleanPhoneTimePlanByBindPhone(BindPhone phone) {
        LOGGER.debug("cleanPhoneTimePlanByBindPhone BindPhone:{}", phone);
        phone = this.bindPhoneDao.findOne(phone.getId());
        if (phone != null) {
            this.phoneTimePlanDao.deleteBindPhoneTimePlan(phone);
            phone.getTimePlanList().clear();
        } else {
            LOGGER.warn("cleanDeptTimePlanByDept未找到对应部门  BindPhone:{}", phone);
        }
    }

    public BindPhone getBindPhoneByCustomerNoAndLsh(String customerNo, String lsh) {
        LOGGER.debug("getBindPhoneByCustomerNoAndLsh customerNo:{}, lsh:{}", customerNo, lsh);
        return this.bindPhoneDao.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
    }
    
    public Boolean getRecordFlag(String customerNo, String lsh) {
        LOGGER.debug("getRecordFlag customerNo:{}, lsh:{}", customerNo, lsh);
        List<Object> list=this.bindPhoneDao.getRecordFlag(customerNo, lsh);
        if(list.size()>1){
        	LOGGER.warn("customerNo:{}, lsh:{},存在重复绑定号码记录", customerNo, lsh);
        }
        return (Boolean) list.get(0);
    }

    @Transactional
    public int modifyAgentStatus(String bindPhoneId, String status) {
        int count = 0;
        BindPhone phone = this.getBindPhone(bindPhoneId);
        if (StringUtils.isNotBlank(phone.getAgentId())) {
            LOGGER.debug("更新坐席状态，400号码：{}, 坐席ID：{}, 状态：{}", phone.getCustomerNo(), phone.getAgentId(), status);
            if(status.startsWith("calling")) {
                count = this.bindPhoneDao.modifyAgentStatusByOldStatus(status, phone.getAgentId(), "idle");
            } else if(status.equals("connect")) {
                count = this.bindPhoneDao.modifyAgentStatusByOldStatus(status, phone.getAgentId(), "calling_true");
            } else {
                count = this.bindPhoneDao.modifyAgentStatus(status, phone.getAgentId());;
            }
        }
        return count;
    }
    
    @Transactional
    public int batchModifyAgentStatus(String bindPhoneId, String status, String uniqueName) {
        int count = 0;
        BindPhone phone = this.getBindPhone(bindPhoneId);
        LOGGER.debug("批量更新坐席状态，400号码：{}, 绑定号码：{}, 状态：{}，uniqueName：{}", phone.getCustomerNo(), phone.getOrigBindPhoneNo(), status, uniqueName);
        if(status.startsWith("calling")) {
            count = this.bindPhoneDao.modifyAgentStatusByOldStatusAndUniqueName(status, phone.getBindPhoneNo(), "idle", uniqueName);
        } else if(status.equals("connect")) {
            count = this.bindPhoneDao.modifyAgentStatusByOldStatusAndUniqueName(status, phone.getBindPhoneNo(), "calling_true", uniqueName);
        } else {
            count = this.bindPhoneDao.modifyAgentStatusByUniqueName(status, phone.getBindPhoneNo(), uniqueName);;
        }
        return count;
    }
    
    @Transactional
    public int batchModifyAgentStatusByBindphone(String bindPhone, String status, String uniqueName) {
        LOGGER.debug("批量更新坐席状态，绑定号码：{}， 状态：{}，uniqueName：{}",  bindPhone, status, uniqueName);
        return this.bindPhoneDao.modifyAgentStatusByUniqueName(status, bindPhone, uniqueName);
    }

    @Transactional
    public void modifyAgentStatusByAgentId(String customerNo, String agentId, String status) {
        LOGGER.debug("更新坐席状态，400号码：{}, 坐席ID：{}, 状态：{}", customerNo, agentId, status);
        this.bindPhoneDao.modifyAgentStatus(status, agentId);
    }

    @Transactional
    public int modifyAgentStatus(String customerNo, String lsh, String status) {
        int count = 0;
        BindPhone phone = this.bindPhoneDao.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        if (StringUtils.isNotBlank(phone.getAgentId())) {
            LOGGER.debug("更新坐席状态，400号码：{}, 坐席ID：{}, 状态：{}", customerNo, phone.getAgentId(), status);
            if(status.startsWith("calling")) {
                count = this.bindPhoneDao.modifyAgentStatusByOldStatus(status, phone.getAgentId(), "idle");
            } else if(status.equals("connect")) {
                count = this.bindPhoneDao.modifyAgentStatusByOldStatus(status, phone.getAgentId(), "calling_false");
            } else {
                count = this.bindPhoneDao.modifyAgentStatus(status, phone.getAgentId());
            }
        }
        return count;
    }
    
    @Transactional
    public int batchModifyAgentStatus(String customerNo, String lsh, String status, String uniqueName) {
        int count = 0;
        BindPhone phone = this.bindPhoneDao.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        LOGGER.debug("批量更新坐席状态，400号码：{}, 绑定号码：{}, 状态：{}", customerNo, phone.getOrigBindPhoneNo(), status);
        if(status.startsWith("calling")) {
            count = this.bindPhoneDao.modifyAgentStatusByOldStatusAndUniqueName(status, phone.getBindPhoneNo(), "idle", uniqueName);
        } else if(status.equals("connect")) {
            count = this.bindPhoneDao.modifyAgentStatusByOldStatusAndUniqueName(status, phone.getBindPhoneNo(), "calling_false", uniqueName);
        } else {
            count = this.bindPhoneDao.modifyAgentStatusByUniqueName(status, phone.getBindPhoneNo(), uniqueName);
        }
        return count;
    }

    @Transactional
    public void modifyAgentCallNum(String bindPhoneId) {
        LOGGER.debug("modifyAgentCallNum bindPhoneId:{}", bindPhoneId);
        this.bindPhoneDao.modifyAgentCallNum(bindPhoneId);
    }

    public String processCallerNo(String callerNo) {
        LOGGER.debug("processCallerNo start callNo:{}", callerNo);
        if(callerNo.startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
            return callerNo;
        }
        String processCallNo = "";
        String districtNo = "";
        String str = callerNo.substring(0, 1);
        if ("0".equals(str)) {
            if ("010".equals(callerNo.substring(0, 3))) {
                processCallNo = callerNo;
            } else if ("01".equals(callerNo.substring(0, 2))) {
                districtNo = this.getDistrictNo(callerNo.substring(1, callerNo.length()));
                if (StringUtils.isEmpty(districtNo)) {
                    districtNo = "000";
                }
                processCallNo = districtNo + callerNo.substring(1, callerNo.length());
            } else {
                processCallNo = callerNo;
            }
        } else {
            districtNo = this.getDistrictNo(callerNo);
            if (StringUtils.isEmpty(districtNo)) {
                districtNo = "000";
            }
            if (Integer.valueOf(str) > 1 || this.commonSettings.getLocalNo().equals(districtNo)) {
                processCallNo = this.commonSettings.getLocalNo() + callerNo;
            } else {
                processCallNo = districtNo + callerNo;
            }
        }
        LOGGER.debug("processCallerNo end return processCallNo:{}", processCallNo);
        return processCallNo;
    }

    public String processCallNo(String callNo) {
        LOGGER.debug("processCallNo start callNo:{}", callNo);
        if(callNo.startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
            return callNo;
        }
        String processCallNo = callNo;
        String districtNo = "";
        if (CALLEE_PREFIX.equals(callNo.substring(0, 1))) {
            if ("010".equals(callNo.substring(0, 3))) {
                if (this.commonSettings.getLocalNo().equals("010")) {
                    processCallNo = callNo.substring(3, callNo.length());
                }
            } else if ("01".equals(callNo.substring(0, 2))) {
				//被叫是手机
				if(this.commonSettings.isMobilePlusLocalArea()) {
					//配置了手机加本地区号
					LOGGER.debug("当前落地配置了手机加本地区号：{}", this.commonSettings.getLocalNo());
					processCallNo = this.commonSettings.getLocalNo() + callNo.substring(1, callNo.length());
				} else {
					districtNo = this.getDistrictNo(callNo.substring(1, callNo.length()));
					if (this.commonSettings.getLocalNo().equals(districtNo)) {
						processCallNo = callNo.substring(1, callNo.length());
					} else {
						if (!CALLEE_PREFIX.equals(this.commonSettings.getPrefix())) {
							processCallNo = districtNo + callNo.substring(1, callNo.length());
						}
					}
				}
            } else {
                if (this.commonSettings.getLocalNo().equals(callNo.substring(0, 3))) {
                    processCallNo = callNo.substring(3, callNo.length());
                } else if (this.commonSettings.getLocalNo().equals(callNo.substring(0, 4))) {
                    processCallNo = callNo.substring(4, callNo.length());
                }
            }
        } else {
            if (Integer.valueOf(callNo.substring(0, 1)) == 1) {
				//被叫是手机
				if(this.commonSettings.isMobilePlusLocalArea()) {
					//配置了手机加本地区号
					LOGGER.debug("当前落地配置了手机加本地区号：{}", this.commonSettings.getLocalNo());
					processCallNo = this.commonSettings.getLocalNo() + callNo;
				} else {
					districtNo = this.getDistrictNo(callNo);
					LocationSetting locationSetting = this.locationSettingDao.getLocationSettingByLocation(districtNo);
					if (locationSetting != null) {
						if (locationSetting.isNeedAdd()) {
							if (!CALLEE_PREFIX.equals(this.commonSettings.getPrefix())) {
								processCallNo = districtNo + callNo;
							} else {
								processCallNo = CALLEE_PREFIX + callNo;
							}
						} else {
							processCallNo = callNo;
						}
					} else if (!this.commonSettings.getLocalNo().equals(districtNo)) {
						if (!CALLEE_PREFIX.equals(this.commonSettings.getPrefix())) {
							processCallNo = districtNo + callNo;
						} else {
							processCallNo = CALLEE_PREFIX + callNo;
						}
					}
				}
                
            }
        }
        LOGGER.debug("processCallNo end return processCallNo:{}", processCallNo);
        return processCallNo;
    }

    public String getAgentStatus(String bindPhoneId) {
        LOGGER.debug("getAgentStatus bindPhoneId:{}", bindPhoneId);
        return this.bindPhoneDao.findOne(bindPhoneId).getStatus();
    }

    public String getAgentStatusByAgentId(String agentId) {
        LOGGER.debug("getAgentStatusByAgentId agentId:{}", agentId);
        List<Object> list = this.bindPhoneDao.getAgentStatusByAgentId(agentId);
        String status = list.isEmpty() ? "" : list.get(0).toString();
        LOGGER.debug("getAgentStatusByAgentId agentStatus = {}", status);
        return status;
    }

    public ColorRing getVoiceBoxAlertRing(String customerNo) {
        List<ColorRing> colorRingList = this.colorRingDao.findColorRingByCustomerNoAndCategory(customerNo,
            BusinessConstants.CATEGORY_VOICEBOX);
        return colorRingList.stream().filter(r -> r.isOnlineFlag()).findAny().orElse(null);
    }

    /**
     * 获取满足时间区域策略的下级部门，如果有多个，返回第一个.
     *
     * @return the child dept by satisfy time plan
     */
    public Dept getChildDeptBySatisfyTimePlan(Dept parentDept, String callerNo) {
        Dept checkedDept = null;
        LOGGER.debug("部门：{} 找到 {} 个子部门", parentDept, parentDept.getChildDepts().size());
        List<Dept> childList = parentDept.getChildDepts();
        User user = this.getUserByNumber(parentDept.getCustomerNo());
        Date now = new Date();
        //部门排序优先级：自定义>节假日>其他(优先级相同根据排序号，排序号相同则随机)
        childList.sort((Dept d1,Dept d2) -> {
            int time1 = d1.getTimeModleOrderInt();
            int time2 = d2.getTimeModleOrderInt();
            int t = time2 - time1;
            if (t != 0) {
                return t;
            }
            long v =  d1.getOrderBy() - d2.getOrderBy();
            if(v != 0) {
                return v > 0 ? 1 : -1;
            } else {
                return d1.getRandomChar().compareTo(d2.getRandomChar());
            }
        });
        for (Dept dept : childList) {
            //判断是否符合当前部门的地区策略
            if (!checkChildDeptAreaModle(dept, callerNo)) {
                continue;
            }
            //判断是否符合时间策略
            if(checkChildDeptTimeModle(user, dept, now)) {
                checkedDept = dept;
                break;
            }
        }
        return checkedDept;
    }

    private boolean checkChildDeptAreaModle(Dept dept, String callerNo) {
        AreaStrategyTemplate areaTemplate = templateService.findDeptAreaTemplate(dept.getCustomerNo(), dept.getDeptLsh());
        LOGGER.debug("获取到地区策略模板：{}", areaTemplate);
        String areaModle = areaTemplate == null ? dept.getAreaModle() : areaTemplate.getDistircts() == null ? "1" : areaTemplate.getDistircts();
        if(ALL_MODLE.equals(areaModle)) {
            return true;
        }
        return checkAreaModle(areaModle, callerNo, "部门流水号:" + dept.getDeptLsh());
    }
    
    private boolean checkChildDeptTimeModle(User user, Dept dept, Date now) {
        String nowTime = DateTimeUtil.formatShortTime(now);
        int week = DateTimeUtil.getWeek(now);
        int preWeek;
        if (week != 1) {
            preWeek = week - 1;
        } else {
            preWeek = 7;
        }
        boolean isHoliday = this.isHoliday(now);
        List<TimeStrategyTemplate> timeTemplates = templateService.findDeptTimeTemplate(dept.getCustomerNo(), dept.getDeptLsh());
        if(timeTemplates == null ){
            LOGGER.debug("未取到时间策略模板数据，使用老的时间策略流程");
            if(!user.isPlatformPlus() && (dept.getTimeModle() == 1)) {
                return true;
            }
            List<DeptTimePlan> timePList = this.deptTimePlanDao.findByCustomerNoAndDeptLshAndChecked(dept.getCustomerNo(), dept.getDeptLsh(), true);
            if((dept.getTimeModle() == 1 || dept.getTimeModle() == 3)&& timePList.isEmpty()) {
                LOGGER.debug("全部/星期时间策略且没有明细，认为满足");
                return true;
            }
            if(dept.getTimeModle() == 2 && timePList.isEmpty() && week < WeekEnum.SATURDAY.getCode()) {
                LOGGER.debug("周一到周五策略且没有明细，认为满足");
                return true;
            }
            for (DeptTimePlan timePlan : timePList) {
                if (WeekEnum.CUSTOM.getCode() == timePlan.getTimeTitle() ) {
                    if (now.after(DateTimeUtil.parseDate(timePlan.getStartTime(), Pattern.SHORTDATETIME)) && now.before(DateTimeUtil.parseDate(timePlan.getEndTime(), Pattern.SHORTDATETIME))) {
                        return true;
                    }
                } else if (WeekEnum.WORKDAY.getCode() == timePlan.getTimeTitle() && week < WeekEnum.SATURDAY.getCode()) {
                    //周一到周五
                    if(!dept.getUser().isPlatformPlus() || DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        //老系统没有时间段
                        return true;
                    }
                } else if (WeekEnum.ALLDAY.getCode() == timePlan.getTimeTitle()) {
                    //全部
                    if(!dept.getUser().isPlatformPlus() || DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        //老系统没有时间段
                        return true;
                    }
                } else if (WeekEnum.HOLIDAY.getCode() == timePlan.getTimeTitle() && isHoliday) {
                    //节假日
                    if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                } else if (WeekEnum.NOTHOLIDAY.getCode() == timePlan.getTimeTitle() && !isHoliday) {
                    //非节假日
                    if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                } else if (preWeek == timePlan.getTimeTitle()) {
                    if (DateTimeUtil.compareToAcrossWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                } else if (week == timePlan.getTimeTitle()) {
                    if (DateTimeUtil.compareToWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())
                            || DateTimeUtil.compareToAfterWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                }
            }
        } else {
            LOGGER.debug("取到时间策略模板：{}", timeTemplates);
            for (TimeStrategyTemplate timePlan : timeTemplates) {
                if(checkTimeWithTemplate(timePlan, now, nowTime, week, preWeek, isHoliday)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkBindPhoneAreaModle(BindPhone phone, String callerNo) {
        AreaStrategyTemplate areaTemplate = templateService.findBindPhoneAreaTemplate(phone.getCustomerNo(), phone.getLsh());
        LOGGER.debug("获取到地区策略模板：{}", areaTemplate);
        String areaModle = areaTemplate == null ? phone.getAreaModle() : areaTemplate.getDistircts() == null ? "1" : areaTemplate.getDistircts();
        if(ALL_MODLE.equals(areaModle)) {
            return true;
        }
        return checkAreaModle(areaModle, callerNo, "绑定号码流水号：" + phone.getLsh());
    }
    
    private boolean checkBindPhoneTimeModle(User user, BindPhone phone, Date now) {
        String nowTime = DateTimeUtil.formatShortTime(now);
        int week = DateTimeUtil.getWeek(now);
        int preWeek;
        if (week != 1) {
            preWeek = week - 1;
        } else {
            preWeek = 7;
        }
        boolean isHoliday = this.isHoliday(now);
        List<TimeStrategyTemplate> timeTemplates = templateService.findBindPhoneTimeTemplate(phone.getCustomerNo(), phone.getLsh());
        if(timeTemplates == null ){
            LOGGER.debug("未取到时间策略模板数据，使用老的时间策略流程");
            if(!user.isPlatformPlus() && (phone.getTimeModle() == 1 || (phone.getTimeModle() == 2 && week < WeekEnum.SATURDAY.getCode()))) {
                return true;
            }
            List<BindPhoneTimePlan> timePlanList = phone.getTimePlanList();
            if((phone.getTimeModle() == 1 || phone.getTimeModle() == 3)&& timePlanList.isEmpty()) {
                LOGGER.debug("全部/星期时间策略且没有明细，认为满足");
                return true;
            }
            if(phone.getTimeModle() == 2 && timePlanList.isEmpty() && week < WeekEnum.SATURDAY.getCode()) {
                LOGGER.debug("周一到周五策略且没有明细，认为满足");
                return true;
            }
            //时间策略：自定义(节假日优先,其他自定义时间判断是否启用)
            for (BindPhoneTimePlan timePlan : timePlanList) {
                if (WeekEnum.CUSTOM.getCode() == timePlan.getTimeTitle()) {
                    //自定义
                    if (now.after(DateTimeUtil.parseDate(timePlan.getStartTime(), Pattern.SHORTDATETIME)) && now.before(DateTimeUtil.parseDate(timePlan.getEndTime(), Pattern.SHORTDATETIME))) {
                        return true;
                    }
                } else if (WeekEnum.WORKDAY.getCode() == timePlan.getTimeTitle() && week < WeekEnum.SATURDAY.getCode()) {
                    //周一到周五
                    if(!user.isPlatformPlus() || DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        //老系统没有时间段
                        return true;
                    }
                } else if (WeekEnum.ALLDAY.getCode() == timePlan.getTimeTitle()) {
                    //全部
                    if(!user.isPlatformPlus() || DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        //老系统没有时间段
                        return true;
                    }
                } else if (WeekEnum.HOLIDAY.getCode() == timePlan.getTimeTitle() && isHoliday) {
                    //节假日
                    if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                } else if (WeekEnum.NOTHOLIDAY.getCode() == timePlan.getTimeTitle() && !isHoliday) {
                    //非节假日
                    if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                } else if (preWeek == timePlan.getTimeTitle()) {
                    if (DateTimeUtil.compareToAcrossWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                } else if (week == timePlan.getTimeTitle()) {
                    if (DateTimeUtil.compareToWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())
                            || DateTimeUtil.compareToAfterWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                        return true;
                    }
                }
            }
        } else {
            LOGGER.debug("取到时间策略模板：{}", timeTemplates);
            for (TimeStrategyTemplate timePlan : timeTemplates) {
                if(checkTimeWithTemplate(timePlan, now, nowTime, week, preWeek, isHoliday)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkTimeWithTemplate(TimeStrategyTemplate timePlan, Date now, String nowTime, int week, int preWeek, boolean isHoliday) {
        if (timePlan.isChecked()) {
            if (WeekEnum.CUSTOM.getCode() == timePlan.getTimeTitle()) {
                //自定义
                if (now.after(DateTimeUtil.parseDate(timePlan.getStartTime(), Pattern.SHORTDATETIME)) && now.before(DateTimeUtil.parseDate(timePlan.getEndTime(), Pattern.SHORTDATETIME))) {
                    return true;
                }
            } else if (WeekEnum.WORKDAY.getCode() == timePlan.getTimeTitle() && week < WeekEnum.SATURDAY.getCode()) {
                //周一到周五
                if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                    return true;
                }
            } else if (WeekEnum.ALLDAY.getCode() == timePlan.getTimeTitle()) {
                //全部
                if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                    return true;
                }
            } else if (WeekEnum.HOLIDAY.getCode() == timePlan.getTimeTitle() && isHoliday) {
                //节假日
                if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                    return true;
                }
            } else if (WeekEnum.NOTHOLIDAY.getCode() == timePlan.getTimeTitle() && !isHoliday) {
                //非节假日
                if (DateTimeUtil.satisfyTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                    return true;
                }
            } else if (preWeek == timePlan.getTimeTitle()) {
                if (DateTimeUtil.compareToAcrossWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                    return true;
                }
            } else if (week == timePlan.getTimeTitle()) {
                if (DateTimeUtil.compareToWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())
                        || DateTimeUtil.compareToAfterWeekTime(nowTime, timePlan.getStartTime(), timePlan.getEndTime())) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkAreaModle(String areaStr, String callerNo, String lsh) {
        String check3 = callerNo.substring(0, 3);
        String check4 = callerNo.substring(0, 4);
        String[] areaArray = areaStr.split(";");
        Optional<String> optional = Arrays.stream(areaArray).filter(area -> area.equals(check3) || area.equals(check4)).findAny();
        LOGGER.debug("主叫：{}，{}，地区策略：{}，是否匹配：{}", callerNo, lsh, areaStr, optional.isPresent());
        return optional.isPresent();
    }

    @Transactional
    public void resetAgentStatus() {
        this.bindPhoneDao.resetAgentStatus(AgentStatusEnum.IDLE.getValue(), AgentStatusEnum.CONNECT.getValue());
    }
    
    @Transactional
    public void resetAgentStatusWithOrder() {
        this.bindPhoneDao.resetAgentStatusWithOrder(AgentStatusEnum.IDLE.getValue(), AgentStatusEnum.CONNECT.getValue());
    }

    @Transactional
    public void startResetAgentStatus(String currentStatus) {
        this.bindPhoneDao.resetAgentStatus(AgentStatusEnum.IDLE.getValue(), currentStatus);
    }
    
    @Transactional
    public void startResetAgentStatusWithOrder(String currentStatus) {
        this.bindPhoneDao.resetAgentStatusWithOrder(AgentStatusEnum.IDLE.getValue(), currentStatus);
    }

    @Transactional
    public void resetAgentCallNum(Integer averageType) {
        String sql = "select id FROM t_base_dept t where t.call_modle=2 and t.average_type=?";
        List<String> list = this.jdbcTemplate.queryForList(sql, String.class, averageType);
        if (!list.isEmpty()) {
            sql = "update t_base_bind_phone set callnum=0 where dept_id=?";
            List<Object[]> params = new ArrayList<>();
			list.forEach(id -> {
				Object[] o = new Object[1];
                o[0] = id;
                params.add(o);
			});
            this.jdbcTemplate.batchUpdate(sql, params);
            try {
				ObjectMapper mapper=new ObjectMapper();
				LOGGER.info("清除的部门Id集合:{}",mapper.writeValueAsString(params));
			} catch (Exception e) {
			}
        }
    }

    public BindPhone getBindPhone(String bindPhoneId) {
        return this.bindPhoneDao.findOne(bindPhoneId);
    }

    public String getAgentStatus(String customerNo, String agentId, String lsh) {
        BindPhone phone = this.bindPhoneDao.getBindPhoneByCustomerNoAndAgentIdAndLsh(customerNo, agentId, lsh);
        return phone != null ? phone.getStatus() : null;
    }

    @Transactional
    public int modifyAgentStatusAndNeedlogin(String bindPhoneId, String status, boolean isNeedlogin) {
        BindPhone phone = this.getBindPhone(bindPhoneId);
        int count = 0;
        if (StringUtils.isNotBlank(phone.getAgentId())) {
            LOGGER.debug("更新坐席状态和登录标识，400号码：{}, 坐席ID：{}, 状态：{}，登陆标识：{}", phone.getCustomerNo(), phone.getAgentId(),
                status, isNeedlogin);
            count = this.bindPhoneDao.modifyAgentStatusAndNeedlogin(status, isNeedlogin, phone.getAgentId());
        }
        return count;
    }
    
    @Transactional
    public int modifyBatchAgentStatusAndNeedlogin(String bindPhoneId, String status, boolean isNeedlogin, String uniqueName) {
        BindPhone phone = this.getBindPhone(bindPhoneId);
        LOGGER.debug("多号更新坐席状态和登录标识，400号码：{}, 绑定号码：{}, 状态：{}，登陆标识：{}", phone.getCustomerNo(), phone.getOrigBindPhoneNo(),
            status, isNeedlogin);
        return this.bindPhoneDao.modifyBatchAgentStatusAndNeedlogin(status, isNeedlogin, phone.getBindPhoneNo(), uniqueName);
    }

    @Transactional
    public void modifyAgentNeedlogin(String bindPhoneId, boolean isNeedlogin) {
        BindPhone phone = this.getBindPhone(bindPhoneId);
        if (StringUtils.isNotBlank(phone.getAgentId())) {
            this.bindPhoneDao.modifyAgentNeedlogin(isNeedlogin, phone.getAgentId());
        }
    }

    @Transactional
    public int modifyBatchAgentNeedlogin(String bindPhoneId, boolean isNeedlogin, String uniqueName) {
        BindPhone phone = this.getBindPhone(bindPhoneId);
        return this.bindPhoneDao.modifyBatchAgentNeedlogin(isNeedlogin, phone.getBindPhoneNo(), uniqueName);
    }
    
    @Transactional
    public void deleteBlackList(BlackList bl) {
        bl = this.blackListDao.findOne(bl.getId());
        bl.getUser().removeBlackList(bl);
        this.blackListDao.delete(bl);
    }

    public Dept getDeptByCustomerNoAndDeptLsh(String customerNo, String deptLsh) {
        return this.deptDao.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
    }

    public List<Object[]> getStatusByCustomerNo(String customerNo) {
        return this.bindPhoneDao.getStatusByCustomerNo(customerNo);
    }

    public BindPhone getBindPhoneByCustomerNoAndReportNum(String customerNo, String reportNum) {
        LOGGER.debug("getBindPhoneByCustomerNoAndReportNum customerNo:{}, reportNum:{}", customerNo, reportNum);
        List<BindPhone> list = this.bindPhoneDao.findBindPhoneByCustomerNoAndReportNumAndStatus(customerNo, reportNum,
            AgentStatusEnum.IDLE.getValue());
		return list.isEmpty() ? null : list.get(0);
    }

    @Deprecated
    @Transactional(readOnly = true)
    public Map<String, BindPhone> getAgentStatusMap(String customerNo) {
        List<BindPhone> list = this.bindPhoneDao.findBindPhoneByCustomerNo(customerNo);
        Map<String, BindPhone> map = new HashMap<>();
		list.forEach(phone -> {
			map.put(phone.getAgentId(), phone);
		});
        return map;
    }

    @Transactional
    public void cleanAgentCallNum(String customerNo, String deptLsh) {
        LOGGER.debug("cleanAgentCallNum customerNo:{}, deptLsh:{}", customerNo, deptLsh);
        if (deptLsh == null) {
            this.bindPhoneDao.cleanAgentCallNumByCustomerNo(customerNo);
        } else {
            Dept dept = this.deptDao.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
            this.bindPhoneDao.cleanAgentCallNumByDeptId(dept);
        }
    }

    @Transactional(readOnly = true)
    public List<BindPhone> getDeptAllAgent(String customerNo) {
        LOGGER.debug("getDeptAllAgent customerNo:{}", customerNo);
        return this.bindPhoneDao.findBindPhoneByCustomerNo(customerNo);
    }

    @Transactional(readOnly = true)
    public boolean isQueueUseable(Dept dept) {
        dept = this.getDept(dept.getId());
        User user = dept.getUser();
        if (dept.getMaxQueueCount() == 0) {
            return false;
        }
        for (BindPhone phone : dept.getBindList()) {
            //判断地区策略,如果有非全部的地区策略，返回false
            AreaStrategyTemplate areaTemplate = templateService.findBindPhoneAreaTemplate(phone.getCustomerNo(), phone.getLsh());
            if(areaTemplate != null && areaTemplate.getDistircts() != null) {
                return false;
            }
            if(areaTemplate == null && !ALL_MODLE.equals(phone.getAreaModle())) {
                return false;
            }
            //判断时间策略
            List<TimeStrategyTemplate> timeTemplates = templateService.findBindPhoneTimeTemplate(phone.getCustomerNo(), phone.getLsh());
            if(timeTemplates != null) {
                for (TimeStrategyTemplate timePlan : timeTemplates) {
                    if(WeekEnum.ALLDAY.getCode() != timePlan.getTimeTitle()) {
                        return false;
                    } else if (!timePlan.getStartTime().equals("00:00") || !timePlan.getEndTime().equals("23:59")){
                        return false;
                    }
                }
            } else {
                if (ALL_MODLE_INT != phone.getTimeModle()) {
                    return false;
                } else {
                    Optional optional = phone.getTimePlanList().stream().filter(timePlan -> user.isPlatformPlus() && (!timePlan.getStartTime().equals("00:00") || !timePlan.getEndTime().equals("23:59"))).findAny();
                    if(optional.isPresent()) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    @Transactional(readOnly = true)
    public boolean isBindPhoneEmpty(String deptId) {
        return this.getDept(deptId).getOnlineBindList().isEmpty();
    }

    public List<BindPhone> getBindPhoneByAgentId(String customerNo, String agentId) {
        return this.bindPhoneDao.getBindPhoneByCustomerNoAndAgentId(customerNo, agentId);
    }

    public void saveInterConnection(InterConnection inter) {
        User user = this.getUserByNumber(inter.getCustomerNo());
        user.addInterConnection(inter);
        this.InterConnectionDao.save(inter);
    }

    public void deleteInterConnection(String userId) {
        this.InterConnectionDao.deleteInterConnection(userId);
        User user = this.getUserByNumber(userId);
        user.getInterConns().clear();

    }

    public void batchUpdateDept(List<String[]> list, String customerNo, User user) {
        String insertSql = "insert into t_base_dept(CUSTOMER_NO,DEPT_LSH,DEPT_NAME,"
            + "CALL_MODLE,CREATE_TIME,PARENT_DEPT_LSH,IVR_KEY,COLOR_RING_LSH,"
            + "CHILD_KEY_LEN,VOICECODE_FLAG,DEPTH,CHILDINTYPE,TIME_MODLE,"
            + "AREA_MODLE,MAX_QUEUE_COUNT,QUEUE_TIMEOUT,AGENT_WAIT_TIME,"
            + "NEXT_DELAY_TIME,SATI_FLAG,GH_FLAG,VOICEBOX_FLAG,"
            + "QUEUE_WAIT_FLAG,ORDER_BY,SALESALERTTONE_LSH,ID,COLORRING_FLAG,"
            + "EXCHANGE_FLAG,LINE_FLAG,RECORD_FLAG,SCREEN_FLAG,USER_ID)"
            + "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,0,0,0,0,0,?)";
        List<Object[]> objList = new ArrayList<Object[]>();
        for (String[] d : list) {
            Object[] obj = new Object[26];
            obj[0] = d[0];
            obj[1] = d[1];
            obj[2] = d[2];
            obj[3] = MyStringUtil.String2Inteter(d[3]);
            obj[4] = null;
            obj[5] = d[6];
            obj[6] = d[7];
            obj[7] = d[8];
            obj[8] = MyStringUtil.String2Inteter(d[10]);
            obj[9] = MyStringUtil.String2Boolean(d[11]);
            obj[10] = MyStringUtil.String2Inteter(d[12]);
            obj[11] = MyStringUtil.String2Inteter(d[13]);
            obj[12] = MyStringUtil.String2Inteter(d[14]);
            obj[13] = d[15];
            obj[14] = MyStringUtil.String2Inteter(d[16]);
            obj[15] = MyStringUtil.String2Inteter(d[17]);
            obj[16] = MyStringUtil.String2Inteter(d[18]);
            obj[17] = MyStringUtil.String2Inteter(d[19]);
            obj[18] = MyStringUtil.String2Boolean(d[20]);
            obj[19] = MyStringUtil.String2Boolean(d[21]);
            obj[20] = MyStringUtil.String2Boolean(d[22]);
            obj[21] = MyStringUtil.String2Boolean(d[23]);
            obj[22] = MyStringUtil.String2Inteter(d[24]);
            obj[23] = d[25];
            obj[24] = UUID.randomUUID().toString();
            obj[25] = user.getId();
            objList.add(obj);
        }
        this.jdbcTemplate.batchUpdate(insertSql, objList);

        StringBuilder deptUpdate = new StringBuilder(
            "update t_base_dept t set  t.color_ring_id = (select c.id from t_base_color_ring c where c.customer_no=t.customer_no and c.lsh=t.color_ring_lsh)");
        deptUpdate
            .append(", t.agent_color_ring_id = (select c.id from t_base_color_ring c where c.customer_no=t.customer_no and c.lsh=t.salesalerttone_lsh)");
        deptUpdate
            .append(", t.parent_dept_id = (select yy.id from (select dd.id,dd.dept_lsh,dd.customer_no from t_base_dept dd,t_base_dept cc where dd.customer_no=cc.customer_no");
        deptUpdate
            .append(" and dd.dept_lsh =cc.parent_dept_lsh group by dd.id,  dd.dept_lsh,  dd.customer_no) yy where yy.customer_no=t.customer_no and yy.dept_lsh=t.parent_dept_lsh) where t.customer_no=?");

        this.jdbcTemplate.update(deptUpdate.toString(), customerNo);
    }

    public void batchUpdateBindPhone(List<String[]> list, String customerNo, List<Dept> depts) {
        Map<String, String> map = new HashMap<>();
		depts.forEach((dept) -> {
			map.put(dept.getCustomerNo() + "_" + dept.getDeptLsh(), dept.getId());
		});

        String insertSql = "insert into t_base_bind_phone(ID,CUSTOMER_NO,LSH,BIND_PHONENO,REPORT_NUM,TIME_MODLE,"
            + "AREA_MODLE,BIND_NUM,CREATE_TIME,CALL_MODLE,WAIT_TIME,ORDER_BY,IS_PBX,STATUS,CALLNUM,"
            + "AGENT_ID,NEED_LOGIN,DEPT_LSH,DEPT_ID)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        List<Object[]> objList = new ArrayList<>();
		list.forEach(d -> {
			Object[] obj = new Object[19];
            obj[0] = UUID.randomUUID().toString();
            obj[1] = d[0];
            obj[2] = d[1];
            obj[3] = d[3];
            obj[4] = d[4];
            obj[5] = MyStringUtil.String2Inteter(d[5]);
            obj[6] = d[6];
            obj[7] = MyStringUtil.String2Inteter(d[7]);
            obj[8] = null;
            obj[9] = MyStringUtil.String2Inteter(d[10]);
            obj[10] = MyStringUtil.String2Inteter(d[11]);
            obj[11] = MyStringUtil.String2Inteter(d[12]);
            obj[12] = MyStringUtil.String2Boolean(d[13]);
            obj[13] = AgentStatusEnum.IDLE.getValue();
            obj[14] = 0;
            obj[15] = d[14];
            obj[16] = 1;
            obj[17] = d[2];
            obj[18] = map.get(d[0] + "_" + d[2]);
            objList.add(obj);
		});
        this.jdbcTemplate.batchUpdate(insertSql, objList);
    }

    public String getDistrictDesc(String fiexdCaller) {
        String no = fiexdCaller.substring(0, 3);
        String desc = districtMap.get(no);
        if (desc == null) {
            no = fiexdCaller.substring(0, 4);
            desc = districtMap.get(no);
            if (desc == null) {
                desc = "未知";
            }
        }
        return desc;
    }
    
    public String getDistrict(String fiexdCaller) {
        String no3 = fiexdCaller.substring(0, 3);
        String no4 = fiexdCaller.substring(0, 4);
        if(districtMap.containsKey(no3)) {
            return no3;
        } else if(districtMap.containsKey(no4)) {
            return no4;
        }else {
            return "未知区号";
        }
    }


    public void initBindPhone() {
        LOGGER.info("开始初始化绑定号码加密。。。");
        LocationSetting location = this.locationSettingDao.getLocationSettingByLocation(this.commonSettings
            .getInitBindPhone());
        if (location == null) {
            List<BindPhone> list = (List<BindPhone>) this.bindPhoneDao.findInitBindPhone();
			list.stream().forEach(phone -> {
                phone.setBindPhoneNo(DES.getTwiceEncString(phone.getBindPhoneNo()));
                this.bindPhoneDao.save(phone);
            });
            LocationSetting setting = new LocationSetting();
            setting.setLocation(this.commonSettings.getInitBindPhone());
            setting.setNeedAdd(true);
            this.locationSettingDao.save(setting);
        }
        LOGGER.info("初始化绑定号码加密完成。。。");
    }

    public List<BindPhone> getBindPhoneByCustomerNoAndAgentIdAndDeptLsh(String customerNo, String agentId,
        String deptLsh) {
        return this.bindPhoneDao.getBindPhoneByCustomerNoAndAgentIdAndDeptLsh(customerNo, agentId, deptLsh);
    }

    public List<BindPhone> getBindPhoneByCustomerNoAndAgentId(String customerNo, String agentId) {
        return this.bindPhoneDao.getBindPhoneByCustomerNoAndAgentId(customerNo, agentId);
    }

    public void createFile(String customerNo, String lsh, String startTime, String filePath) {
        try {
            File file = new File(this.midwareSettings.getVocDataPath() + filePath);
            if (!file.exists()) {
                String cloudStorageUser = this.commonSettings.getCloudStorageUser();
                String cloudStoragePassword = this.commonSettings.getCloudStoragePassword();
                CloudStorageClient client = new CloudStorageClient(cloudStorageUser, cloudStoragePassword);
                DownLoadResponse rs = client.checkResource(lsh, startTime, System.currentTimeMillis());
                if ("0".equals(rs.getCode())) {
                    InputStream is = client.downloadDefaultFile(lsh, startTime, System.currentTimeMillis())
                        .getInputStream();
                    FileUtils.copyInputStreamToFile(is, file);
                } else {
                    LOGGER.warn("客户:{}验证对应文件Id:{}不存在", customerNo, lsh);
                }
            }
        } catch (Exception e) {
            LOGGER.error("客户:{}对应文件Id:{}处理异常:{}", customerNo, lsh, e);
        }
    }

    public void createFile(String customerNo, String alyFileUrl, String filePath) {
        try {
            LOGGER.debug("客户:{},阿里云url:{},本地存储路径:{}", customerNo, alyFileUrl, this.midwareSettings.getVocDataPath()
                + filePath);
            File file = new File(this.midwareSettings.getVocDataPath() + filePath);
            if (file.exists() && file.length() < 1024) {
                file.delete();
                HttpHelper.httpDownloadFile(alyFileUrl, file);
            } else {
                //                String cloudStorageUser = this.commonSettings.getCloudStorageUser();
                //                String cloudStoragePassword = this.commonSettings.getCloudStoragePassword();
                //                CloudStorageClient client = new CloudStorageClient(cloudStorageUser, cloudStoragePassword);
                //                InputStream is = client.downloadFileByUrl(alyFileUrl).getInputStream();
                //                FileUtils.copyInputStreamToFile(is, file);
                HttpHelper.httpDownloadFile(alyFileUrl, file);
            }
            LOGGER.debug("客户:{},文件下载路径:{},同步完成", customerNo, alyFileUrl);
        } catch (Exception e) {
            LOGGER.error("客户:{}文件下载:{},处理异常:{}", customerNo, alyFileUrl, e);
        }
    }

    @PostConstruct
    private void initDistrictData() {
        String sql = "select t.district_no,t.city,t.province from t_base_mobile_location t "
            + "where t.district_no is not null GROUP BY t.district_no,t.city,t.province ";
        List<Map<String, Object>> list = this.jdbcTemplate.queryForList(sql);
        list.forEach(m -> {
			String districtNo = m.get("district_no").toString();
            String province = m.get("province").toString();
            String city = m.get("city").toString();
			String desc;
            if (city.equals(province)) {
                desc = city + "市";
            } else {
            	if (province.contains("省")) {
            		desc = province + city + "市";
            	}else {
            		desc = province + "省 " + city + "市";
            	}
            }
            districtMap.put(districtNo, desc);
		});
    }

    public boolean inGlobalBlack(String caller) {
        String sql = "SELECT COUNT(1) FROM t_base_global_black WHERE `no` like left(?, LENGTH(`no`))";
        long count = this.jdbcTemplate.queryForObject(sql, Long.class, caller);
        return count > 0;
    }

    @Transactional
    public String getDeptBindphoneDetails(String deptId) {
        Dept dept = this.getDept(deptId);
        List<BindPhone> bindPhoneList = this.bindPhoneDao.findBindPhoneByDept(dept);
        StringBuilder sb = new StringBuilder();
        sb.append(dept.toString());
		bindPhoneList.forEach((bp) -> {
			sb.append(bp.getDetails()).append("；");
		});
        return sb.toString();
    }

    @Transactional
    public String getDeptBindphoneDetails(String customerNo, String deptLsh) {
        Dept dept = this.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        return dept == null ? ("根据400号码：" + customerNo + ",部门流水号：" + deptLsh + "，未查到部门") : this
            .getDeptBindphoneDetails(dept.getId());
    }
	
	public Dept findDeptByParentDeptAndIvrKey(Dept parentDept, String ivrKey) {
        return this.deptDao.findDeptByParentDeptAndIvrKey(parentDept, ivrKey);
    }
	
	public List<String> findMainCallByNumber(String number) {
		String sql = "select t.mainCall from t_maincall_config t where t.number = ?";
		return this.jdbcTemplate.queryForList(sql, String.class, number);
	}
	
	public void updateReissueCode(String customerNo,String keepGlobal,String originalCall,String reissueCode){
		try {
			LOGGER.debug("updateReissueCode customerNo:{},keepGlobal:{}, originalCall:{},"
					+ "reissueCode:{},",customerNo,keepGlobal,originalCall,reissueCode);
			if(StringUtils.isNotEmpty(keepGlobal) && MyStringUtil.String2Inteter(keepGlobal)>0){
				User user=this.getUserByNumber(customerNo);
				if(user!=null){
					if(StringUtils.isNotEmpty(originalCall) && StringUtils.isNotEmpty(reissueCode)){
							String sql="select count(1) from cti_server.original_call where original_no=?";
							Long count=jdbcTemplate.queryForObject(sql, Long.class,user.getOriginalNo());
							if(count>0){
								sql="update cti_server.original_call set redirecting_number=?,original=? where original_no=?";
							}else{
								sql="insert into cti_server.original_call(redirecting_number,original,original_no)values(?,?,?)";
							}
							jdbcTemplate.update(sql,reissueCode,originalCall,user.getOriginalNo());
					}else{
						String sql="delete from cti_server.original_call where original_no=?";
						jdbcTemplate.update(sql,user.getOriginalNo());
					}
				}
			}
		} catch (Exception e) {
			LOGGER.error("{}:设置全局改发规则异常:", customerNo, e);
		}
	}
	
	public List<TalkNoteDto> findByDeptAndMainCall(Dept dept, String mainCall){
		StringBuilder sql=new StringBuilder("select callee_no,incoming_time from t_business_talk_note ");
		sql.append(" where caller_no=? and customer_no=? and dept_id=? and incoming_time>=?");
		sql.append(" and talk_type in(3,4) order by incoming_time desc limit 1");
		return jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<TalkNoteDto>(TalkNoteDto.class), 
				mainCall,dept.getCustomerNo(),dept.getDeptLsh(),DateTimeUtil.addDay(-1));
	}
	
	public List<TalkNoteDto> findByCustomerNoAndDeptLshAndMainCall(String customerNo, String deptLsh, String mainCall){
		StringBuilder sql=new StringBuilder("select callee_no,incoming_time from t_business_talk_note ");
		sql.append(" where caller_no=? and customer_no=? and dept_id=? and incoming_time>=?");
		sql.append(" and talk_type in(3,4) order by incoming_time desc limit 1");
		return jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<TalkNoteDto>(TalkNoteDto.class),  
				mainCall,customerNo,deptLsh,DateTimeUtil.addDay(-1));
	}
	
	 public void doBakNoteAndRecord() {
        java.util.Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.DAY_OF_MONTH, c1.get(Calendar.DAY_OF_MONTH) - 7);
        c1.set(Calendar.HOUR_OF_DAY, 0);
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);

        //备份清单表的列
        String noteColumn = "id,callee_no,callee_offhook_time,caller_no,"
            + "  caller_offhook_time,customer_no,dept_id,end_flag,gh,incoming_time,lsh,onhook_mode,onhook_time,customer_original_no,outgoing_time,talk_interval,talk_type";
        String recordColumn = " id,callee_no,caller_no,customer_no,end_flag,end_time,file_name,file_fullname,record_interval,start_time,talk_note_id";

        String deleteNote = " delete from t_business_talk_note where incoming_time<= ?";
        String deleteRecord = " delete from t_business_voice_record  where start_time<= ?";
        String deleteVoice = " delete from t_business_voice_box  where start_time<= ?";
        this.jdbcTemplate.update(deleteNote, c1.getTime());
        this.jdbcTemplate.update(deleteRecord, c1.getTime());
        this.jdbcTemplate.update(deleteVoice, c1.getTime());
    }
	 
	public int cleanData(String userId) {
        int i;
        try {
            String sql = "select delete_user_info(?)";
            i = this.jdbcTemplate.queryForObject(sql, Integer.class, userId);
        } catch (Exception e) {
            i = -1;
            LOGGER.error("号码数据清除异常:{}", e);
        }
        return i;
    }
	
	public boolean isCustomerFunctionOpen(String numberCode, String function) {
		String sql = "SELECT t.on_off FROM t_customer_switch t where t.`no` = ? and t.`function` = ?";
		List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, numberCode, function);
		if(list.isEmpty()) {
			return true;
		} else {
			String flag = (String) list.get(0).get("on_off");
			return !"off".equalsIgnoreCase(flag);
		}
	}
	
	public String getCustomerFunctionData(String numberCode, String function) {
		String sql = "SELECT t.on_off FROM t_customer_switch t where t.`no` = ? and t.`function` = ?";
		List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, numberCode, function);
		return list.isEmpty() ? null : (String) list.get(0).get("on_off");
	}
	
	public boolean isNotRecordConvertWhiteList(String numberCode){
		String sql="select count(1) from t_record_convert_white_list where customer_no=?";
		boolean flag= jdbcTemplate.queryForObject(sql, Long.class,numberCode)==0;
		LOGGER.info("{}:录音识别白名单检查返回值:{}",numberCode,flag);
		return flag;
	}
	
	@Transactional
	public void saveOrUpdatePbxInfo(BindPhone phone,Integer useCount){
		Date now=new Date();
		String createDate=DateTimeUtil.formatDate(now);
		List<PbxInfo> pbxList=pbxInfoDao.findByCustomerNoAndCreateDate(phone.getCustomerNo(), createDate);
		if(pbxList.isEmpty()){
			PbxInfo pbx=new PbxInfo();
			pbx.setCustomerNo(phone.getCustomerNo());
			pbx.setBindPhone(phone.getOrigBindPhoneNo());
			pbx.setCreateDate(createDate);
			pbx.setUpdateTime(now);
			pbx.setUseCount(useCount);
			pbx.setMaxCount(phone.getBindNum());
			pbxInfoDao.save(pbx);
			return;
		}
		PbxInfo pbx=pbxList.get(0);
		List<PbxInfo> deletList=Lists.newArrayList();
		if(pbxList.size()>1){
			for(PbxInfo info : pbxList){
				if(info.getUseCount().intValue()>useCount.intValue()){
					pbx=info;
					break;
				}
			}
			for(PbxInfo info : pbxList){
				if(info.getId().equals(pbx.getId())){
					continue;
				}
				deletList.add(info);
			}
			pbxInfoDao.delete(deletList);
		}
		if(pbx.getUseCount().intValue()<useCount.intValue()){
			pbx.setUpdateTime(now);
			pbx.setUseCount(useCount);
		}
		pbxInfoDao.save(pbx);
	} 
	
    public SecondSaleNo getSecondSaleNo(String originalNo) {
        List<SecondSaleNo> list = this.secondSaleDao.findByOriginalNo(originalNo);
        return list.isEmpty() ? null : list.get(0);
    }
    
    @Transactional
    public void updateDeptLastBindPhone(String customNo, String deptLsh, String bindPhoneLsh) {
        deptDao.updateLastBindPhone(customNo, deptLsh, bindPhoneLsh);
    }
    
    public String findCrbtTemplateId(Dept dept) {
        List<TimeStrategyTemplate> templates = this.templateService.findCrbtTimeTemplate(dept.getCrbtRingTemplateIds());
        if(templates.isEmpty()) {
            return null;
        }
        //如果有时间段重复的，自定义>节假日>其他
//        templates.sort((TimeStrategyTemplate r1, TimeStrategyTemplate r2) -> {
//            int o1 = r1.getOrderInt();
//            int o2 = r2.getOrderInt();
//            int t = o2 - o1;
//            if (t != 0) {
//                return t;
//            }
//            return r1.getRandomChar().compareTo(r2.getRandomChar());
//        });
        List<TimeStrategyTemplate> sortedList = new ArrayList<>();
        List<TimeStrategyTemplate> orderList = templates.stream().filter(t -> t.getOrderInt() > 0).collect(Collectors.toList());
        List<TimeStrategyTemplate> randomList = templates.stream().filter(t -> t.getOrderInt() == 0).collect(Collectors.toList());
        orderList.sort((TimeStrategyTemplate r1, TimeStrategyTemplate r2) -> {
            return r2.getOrderInt() - r1.getOrderInt();
        });
        Collections.shuffle(randomList);
        sortedList.addAll(orderList);
        sortedList.addAll(randomList);
        
        Date now = new Date();
        String nowTime = DateTimeUtil.formatShortTime(now);
        boolean isHoliday = this.isHoliday(now);
        int week = DateTimeUtil.getWeek(now);
        int preWeek;
        if (week != 1) {
            preWeek = week - 1;
        } else {
            preWeek = 7;
        }
        for (TimeStrategyTemplate timePlan : templates) {
            if(checkTimeWithTemplate(timePlan, now, nowTime, week, preWeek, isHoliday)) {
                return timePlan.getTemplateId();
            }
        }
        return null;
    }
    
    public CrbtRing findCrbtRingWithTemplate(Dept dept) {
        String templateId = this.findCrbtTemplateId(dept);
        if(templateId != null) {
            return dept.getCrbtRingByTemplateId(templateId);
        }
        return null;
    }
    
    public boolean isHoliday(Date date) {
        String sql = "select count(1) from day_setting where year_month_day = ? and is_work_day = 2";
        Long count = this.jdbcTemplate.queryForObject(sql, Long.class, DateTimeUtil.format(date, Pattern.SHORTDATE));
        return count > 0;
    }
    
    public ColorRing getColorRing(String id) {
        return this.colorRingDao.findOne(id);
    }
   
    public void addOfflineCache(String numberCode, String deptLsh, String caller) {
        String key = numberCode + "_" + deptLsh + "_" + caller;
        CacheUtil.put(CacheUtil.CACHE_OFFLINE_NAME, key, caller);
    }
    
    public List<BlackList> getBlackListWithIvrKey(String customerNo, String ivrKey) {
        return this.blackListDao.findBlackListByCustomerNoAndExt(customerNo, ivrKey);
    }
    
    public WhiteList getWhiteList(String customerNo, Long xh) {
        List<WhiteList> list = this.whiteListDao.findWhiteListByCustomerNoAndXh(customerNo, xh);
        return list.isEmpty() ? null : list.get(0);
    }
    
    public int getWhiteListCount(String customerNo, Long xh) {
        WhiteList whiteList = this.getWhiteList(customerNo, xh);
        return whiteList == null ? 0 : whiteList.getCallCount();
    }
    
    public long checkCustomerWhiteList(String numberCode, String caller) {
        String fixedCaller = this.getFixedCaller(caller);
        List<WhiteList> whiteList = this.whiteListDao.findWhiteListByCustomerNoAndUserNoAndAddDateAfter(numberCode, fixedCaller, DateUtils.addYears(new Date(), -1));
        if (!whiteList.isEmpty()) {
            for (WhiteList white : whiteList) {
                return white.getCallCount() <= 0 ? BusinessConstants.CUSTOMER_WHITE_LIST_COUNT_LIMIT : white.getXh();
            }
        }
        return BusinessConstants.CUSTOMER_WHITE_LIST_NOT_IN;
    }
    
    private String getFixedCaller(String caller) {
        String fixedCaller = caller;
        if (MyStringUtil.isMobile(caller)) {
            fixedCaller = caller.startsWith("0") ? caller.substring(1) : caller;
        } else {
            //本地固话补上本地区号
            if (!caller.startsWith("0")) {
                fixedCaller = this.commonSettings.getLocalNo() + caller;
            }
        }
        return fixedCaller;
    }
    
    @Transactional
    public WhiteList subtractWhiteListCount(String customerNo, Long xh) {
        this.whiteListDao.subtractCount(customerNo, xh);
        return getWhiteList(customerNo, xh);
    }
    
    public Dept findDeptByKeyWords(String numberCode, String keyWords) {
        List<Dept> list = this.deptDao.findDeptByCustomerNoAndAudioKeywords(numberCode, keyWords);
        return list.isEmpty() ? null : list.get(0);
    }
    
    public CrbtRing getCrbtRing(String id) {
        return this.crbtRingDao.findOne(id);
    }
    
    public Dept findTopDeptByCustomerNoAndIvrKey(String customerNo, String ivrKey) {
        return deptDao.findFirstByCustomerNoAndIvrKeyOrderByDepth(customerNo, ivrKey);
    }
    
    public Dept getDeptByCustomerNoAndIvrKeyAndDepth(String customerNo, String ivrKey, Integer depth) {
        return deptDao.findFirstByCustomerNoAndIvrKeyAndDepth(customerNo, ivrKey, depth);
    }
    
    public Dept findByCustomerNoAndAreaCode(String customerNo, String areaCode) {
        return deptDao.findFirstByCustomerNoAndAreaCode(customerNo, areaCode);
    }
    
    public RobotAgentConfig getRobotAgentConfig(String numberCode){
        List<RobotAgentConfig> list = robotAgentConfigDao.findByNumberCode(numberCode);
        return list.isEmpty() ? null : list.get(0);
    }
    
    @Transactional
    public void updateBaseConfig(BaseConfigDto dto) {
        //更新数据库配置
        String sql = "delete from t_base_config";
        this.jdbcTemplate.update(sql);
        sql = "INSERT INTO `t_base_config` (`main_server`, `backup_url`, `rmi_service`, `create_time`) VALUES (?,?,?,?)";
        this.jdbcTemplate.update(sql, dto.getMainServer(), dto.getBackupUrl(), dto.getRmiService(), new Date());
        //刷新Rmi配置
        SpringHelper.getBean(DynamicRmiRegister.class).registerRmi();
    }
}
