/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.sungoin.netphoneLocal.business.bean.AgentBean;
import com.sungoin.netphoneLocal.business.bean.AgentStatusEnum;
import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import com.sungoin.netphoneLocal.business.dao.BindPhoneRepository;
import com.sungoin.netphoneLocal.business.dao.FileTaskDao;
import com.sungoin.netphoneLocal.business.dao.OutDataTaskDao;
import com.sungoin.netphoneLocal.business.dao.TalkNoteRepository;
import com.sungoin.netphoneLocal.business.dao.TaskDelayUploadNumberDao;
import com.sungoin.netphoneLocal.business.dao.VoiceBoxRepository;
import com.sungoin.netphoneLocal.business.dao.VoiceRecordRepository;
import com.sungoin.netphoneLocal.business.dao.VoiceScoreRepository;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.FileTask;
import com.sungoin.netphoneLocal.business.po.OutDataTask;
import com.sungoin.netphoneLocal.business.po.OutDataType;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.VoiceBox;
import com.sungoin.netphoneLocal.business.po.VoiceRecord;
import com.sungoin.netphoneLocal.business.po.VoiceScore;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.util.CacheUtil;
import com.sungoin.netphoneLocal.util.ConcurrentHelper;
import java.text.DateFormat;

import java.util.Date;
import java.util.Deque;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

/**
 * <AUTHOR> 2015-7-29
 */
@Service
public class BusinessService {

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = LoggerFactory
            .getLogger(BusinessService.class);

    @Resource
    private TalkNoteRepository talkNoteDao;

    @Resource
    private VoiceRecordRepository recordDao;

    @Resource
    private VoiceScoreRepository scoreDao;

    @Resource
    private VoiceBoxRepository voiceBoxDao;

    @Resource
    private BaseService baseService;

    @Resource
    CommonSettings commonSettings;

    @Resource
    OutDataTaskDao outDataTaskDao;

    @Resource
    FileTaskDao fileTaskDao;

    @Resource
    BindPhoneRepository bindRepository;

    @Resource
    TaskDelayUploadNumberDao uploadNumberDao;

    private static final Map<String, Boolean> ORDER_MAP = new ConcurrentHashMap();

    private static final String EXP = "_";

    public void saveOrUpdate(TalkNote note) {
        this.talkNoteDao.save(note);
    }

    public TalkNote get(String id) {
        return this.talkNoteDao.findOne(id);
    }

    public void delete(Object obj) {
        if (obj instanceof TalkNote) {
            this.talkNoteDao.delete((TalkNote) obj);
        } else if (obj instanceof VoiceRecord) {
            this.recordDao.delete((VoiceRecord) obj);
        } else {
            throw new UnsupportedOperationException("Not supported yet.");
        }
    }

    public VoiceRecord getVoiceRecordByTalkNoteId(String talkNoteId) {
        return this.recordDao.getVoiceRecordByTalkNoteId(talkNoteId);
    }

    public VoiceScore getVoiceScoreByTalkNoteId(String talkNoteId) {
        return this.scoreDao.getVoiceScoreByTalkNoteId(talkNoteId);
    }

    public VoiceBox getVoiceBoxByTalkNoteId(String talkNoteId) {
        return this.voiceBoxDao.getVoiceBoxByTalkNoteId(talkNoteId);
    }

    public void saveOrUpdateVoiceBox(VoiceBox box) {
        this.voiceBoxDao.save(box);
    }

    public void saveOrUpdateVoiceRecord(VoiceRecord record) {
        this.recordDao.save(record);
    }

    public void saveOrUpdateVoiceScore(VoiceScore score) {
        this.scoreDao.save(score);
    }

    public Deque<AgentBean> createQueue(String deptId) {
        ConcurrentLinkedDeque<AgentBean> queue = new ConcurrentLinkedDeque<AgentBean>();
        CacheUtil.put(CacheUtil.CACHE_NAME_QUEUE, deptId, queue);
        return (Deque<AgentBean>) CacheUtil.get(CacheUtil.CACHE_NAME_QUEUE, deptId);
    }

    public int getQueueSizeByDept(String deptId) {
        Deque<AgentBean> queue = this.getQueue(deptId);
        if (queue != null) {
            return queue.size();
        } else {
            return 0;
        }
    }
    
    public String getQueueInfo(String deptId) {
        Deque<AgentBean> queue = this.getQueue(deptId);
        if (queue == null) {
            return "当前队列为空！";
        } else {
            StringBuilder sb = new StringBuilder();
            for (AgentBean bean : queue) {
                sb.append(bean.getCallerId().substring(0, bean.getCallerId().indexOf("_"))).append("-->").append(DateFormatUtils.format(bean.getQueueStratTime(), "HH:mm:ss.SSS")).append(";");
            }
            return sb.toString();
        }
    }

    public int[] getQueueStatus(String deptId, String callerId) {
        int[] result = new int[]{-1, 0};
        int status = 0;
        int index = 0;
        Deque<AgentBean> queue = this.getQueue(deptId);
        if (queue != null) {
            Dept dept = this.baseService.getDept(deptId);
            for (AgentBean bean : queue) {
                if (callerId.equals(bean.getCallerId())) {
                    if (BusinessConstants.QUEUE_WAIT == bean.getQueueStatus()) {
                        long min = dept.getQueueTimeout() * 1000;
                        long poor = System.currentTimeMillis()
                                - bean.getQueueStratTime();
                        if (poor >= min) {
                            if (index == 0 && bindRepository.findIdleAgentCountByDeptIdAndStatus(deptId, AgentStatusEnum.IDLE.getValue()) > 0) {
                                LOGGER.debug("存在空闲坐席,主叫：{} 状态变成可接入了", bean.getCallerId());
                                bean.setQueueStatus(BusinessConstants.WAIT_CONNECT);
                            } else {
                                LOGGER.debug("无空闲坐席,主叫：{} 状态变成超时了", bean.getCallerId());
                                bean.setQueueStatus(BusinessConstants.QUEUE_TIMEOUT);
                            }
                        }
                    }
                    status = bean.getQueueStatus();
                    break;
                }
                index++;
            }
            result[0] = status;
            result[1] = index;
        }
        return result;
    }

    public int addToQueue(String deptId, String callerId) {
        Dept dept = this.baseService.getDept(deptId);
        Deque<AgentBean> queue = this.getQueue(deptId);
        if(queue == null) {
            queue = this.createQueue(dept.getId());
        }
        String key = dept.getCustomerNo() + "_" + dept.getDeptLsh() + "_" + callerId.split("_")[0];
        Object cache = CacheUtil.removeAndReturn(CacheUtil.CACHE_OFFLINE_NAME, key);
        boolean offlineFlag = cache != null;
        LOGGER.debug("{} 离线排队标识：{}", key, offlineFlag);
        if (offlineFlag || queue.size() < dept.getMaxQueueCount()) {
            AgentBean bean = new AgentBean();
            bean.setCallerId(callerId);
            bean.setQueueStatus(BusinessConstants.QUEUE_WAIT);
            bean.setQueueStratTime(System.currentTimeMillis());
            if(offlineFlag) {
                queue.addFirst(bean);
            } else {
                queue.add(bean);
            }
        }
        int size = queue.size();
        LOGGER.debug("当前队列人数：{}", size);
        return size;
    }
    
    public int updateQueueState(String deptId, String callId, int queueState) {
        Deque<AgentBean> queue = this.getQueue(deptId);
        if (queue != null) {
            for (AgentBean bean : queue) {
                if (bean.getCallerId().equals(callId)) {
                    bean.setQueueStatus(queueState);
                    bean.setQueueStratTime(System.currentTimeMillis());
                    LOGGER.debug("主叫：{} 状态变为：{}", bean.getCallerId(), queueState);
                    break;
                }
            }
        }
        return queue == null ? 0 : queue.size();
    }
    
    public boolean queueExist(String deptId, String callId) {
        Deque<AgentBean> queue = this.getQueue(deptId);
        if(queue == null) {
            return false;
        }
        return queue.stream().anyMatch(ab -> ab.getCallerId().equals(callId));
    }

    public int removeQueue(String deptId, String callerId, boolean spreadState) {
        Deque<AgentBean> queue = this.getQueue(deptId);
        int state = BusinessConstants.QUEUE_WAIT;
        if (queue != null) {
            for (AgentBean bean : queue) {
                if (bean.getCallerId().equals(callerId)) {
                    state = bean.getQueueStatus();
                    LOGGER.debug("主叫：{} 从队列中移除了", bean.getCallerId());
                    queue.remove(bean);
                    break;
                }
            }
            if (queue.isEmpty()) {
                LOGGER.debug("队列大小为0,队列从缓存中移除");
                CacheUtil.remove(CacheUtil.CACHE_NAME_QUEUE, deptId);
            } else if (spreadState && BusinessConstants.WAIT_CONNECT == state) {
                for (AgentBean bean : queue) {
                    if (BusinessConstants.WAIT_CONNECT != bean.getQueueStatus()) {
                        LOGGER.debug("主叫：{} 变成待接听了", bean.getCallerId());
                        bean.setQueueStatus(BusinessConstants.WAIT_CONNECT);
                        break;
                    }
                }
            }
        }
        return queue == null ? 0 : queue.size();
    }

    public int continueWait(String deptId, String callerId) {
        Deque<AgentBean> queue = this.getQueue(deptId);
        int status = 0;
        int index = 0;
        if (queue != null) {
            for (AgentBean bean : queue) {
                if (callerId.equals(bean.getCallerId())
                        && BusinessConstants.QUEUE_TIMEOUT == bean.getQueueStatus()) {
                    if (index == 0 && bindRepository.findIdleAgentCountByDeptIdAndStatus(deptId, AgentStatusEnum.IDLE.getValue()) > 0) {
                        LOGGER.debug("存在空闲坐席,主叫：{} 状态变成可接入了", bean.getCallerId());
                        bean.setQueueStatus(BusinessConstants.WAIT_CONNECT);
                    } else {
                        LOGGER.debug("主叫：{} 由状态：{} 变成了:{}", bean.getCallerId(),
                            bean.getQueueStatus(), BusinessConstants.QUEUE_WAIT);
                        bean.setQueueStatus(BusinessConstants.QUEUE_WAIT);
                        bean.setQueueStratTime(System.currentTimeMillis());
                    }
                    status = bean.getQueueStatus();
                    break;
                }
                index++;
            }
        }
        return status;
    }
    
    public int getQueueIndex(String deptId, String callerId) {
        Deque<AgentBean> queue = this.getQueue(deptId);
        if(queue == null) {
            return -1;
        }
        int index = 0;
        for (AgentBean bean : queue) {
            index ++;
            if(bean.getCallerId().equals(callerId)) {
                break;
            }
        }
        return index;
    }

    public void MessageAgentOnHook(String deptId) {
        ConcurrentHelper.doInBackground(new Runnable() {
            @Override
            public void run() {
                try {
                    Dept dept = baseService.getDept(deptId);
                    boolean notified = false;
                    Deque<AgentBean> queue = BusinessService.this.getQueue(deptId);
                    LOGGER.debug("400号码：{}，找到队列当前大小：{}，deptId：{}", dept.getCustomerNo(), queue == null ? "队列不存在" : queue.size(), deptId);
                    if (queue != null) {
                        for (AgentBean bean : queue) {
                            if (BusinessConstants.WAIT_CONNECT != bean.getQueueStatus()) {
                                LOGGER.debug("坐席挂机，主叫：{} 变成待接听了", bean.getCallerId());
                                bean.setQueueStatus(BusinessConstants.WAIT_CONNECT);
                                notified = true;
                                break;
                            }
                        }
                    }
                    if(notified) {
                        TimeUnit.SECONDS.sleep(1);
                    }
                } catch (Exception ex) {
                    LOGGER.error(ex.getMessage());
                }
            }
        });

    }

    public void notifyQueueAgentOnhook(String customerNo, String agentId) {
        ConcurrentHelper.doInBackground(new Runnable() {
            @Override
            public void run() {
                try {
                    List<BindPhone> bps = baseService.getBindPhoneByAgentId(customerNo, agentId);
                    boolean notified = false;
                    outerloop:
                    for (BindPhone phone : bps) {
                        String deptId = phone.getDept().getId();
                        Deque<AgentBean> queue = BusinessService.this.getQueue(deptId);
                        LOGGER.debug("400号码：{}，绑定号码：{},找到队列当前大小：{}，deptId：{}", customerNo, phone.getOrigBindPhoneNo(), queue == null ? "队列不存在" : queue.size(), deptId);
                        if (queue != null) {
                            for (AgentBean bean : queue) {
                                if (BusinessConstants.WAIT_CONNECT != bean.getQueueStatus()) {
                                    LOGGER.debug("坐席挂机，主叫：{} 变成待接听了", bean.getCallerId());
                                    bean.setQueueStatus(BusinessConstants.WAIT_CONNECT);
                                    notified = true;
                                    break outerloop;
                                }
                            }
                        }
                    }
                    if(notified) {
                        TimeUnit.SECONDS.sleep(1);
                    }
                } catch (Exception ex) {
                    LOGGER.error(ex.getMessage());
                }
            }
        });
    }

    public void MessageCallerOnHook(String deptId, String callerId) {
        LOGGER.debug("主叫挂机,移除队列");
        this.removeQueue(deptId, callerId, false);
    }

    private Deque<AgentBean> getQueue(String deptId) {
        ConcurrentLinkedDeque<AgentBean> queue = null;
        Object obj = CacheUtil.get(CacheUtil.CACHE_NAME_QUEUE, deptId);
        if (obj != null && obj instanceof ConcurrentLinkedDeque) {
            queue = (ConcurrentLinkedDeque<AgentBean>) obj;
        }
        return queue;
    }

    public List<Object[]> getDeptQueueSize(String customerNo) {
        List<Object[]> result = new ArrayList<>();
        User user = this.baseService.getUserByNumber(customerNo);
        if (user != null) {
            user.getDepts().forEach((dept) -> {
                int size = this.getQueueSizeByDept(dept.getId());
                if (size != 0) {
                    Object[] obj = new Object[2];
                    obj[0] = dept.getDeptLsh();
                    obj[1] = size;
                    result.add(obj);
                }
            });
        }
        return result;
    }

    public void putConnect(String bindPhoneId) {
        CacheUtil.put(CacheUtil.CACHE_CONNECT_NAME,
                this.getConnectKey(bindPhoneId), new AtomicInteger());
    }

    public void addConnect(String bindPhoneId) {
        LOGGER.debug("{}:坐席增加通话数", bindPhoneId);
        Object obj = CacheUtil.get(CacheUtil.CACHE_CONNECT_NAME,
                this.getConnectKey(bindPhoneId));
        if (obj != null) {
            AtomicInteger count = (AtomicInteger) obj;
            LOGGER.debug("{} :坐席增加前通话数: {}", bindPhoneId, count);
            int connectCount = count.incrementAndGet();
            LOGGER.debug("{} :坐席增加后通话数: {}", bindPhoneId, connectCount);
        }
    }

    public int getConnectCount(String bindPhoneId) {
        Object obj = CacheUtil.get(CacheUtil.CACHE_CONNECT_NAME,
                this.getConnectKey(bindPhoneId));
        if (obj != null) {
            AtomicInteger count = (AtomicInteger) obj;
            return count.get();
        } else {
            this.putConnect(bindPhoneId);
            return 0;
        }
    }
    
    public int getRobotConnectCount(String customerNo, String bindPhone) {
        String key = customerNo + "_" + bindPhone;
        Object obj = CacheUtil.get(CacheUtil.CACHE_CONNECT_NAME, key);
        if (obj != null) {
            AtomicInteger count = (AtomicInteger) obj;
            return count.get();
        } else {
            this.putRobotConnect(key);
            return 0;
        }
    }
    
    public void putRobotConnect(String key) {
        CacheUtil.put(CacheUtil.CACHE_CONNECT_NAME, key, new AtomicInteger());
    }
    
    public void addRobotConnect(String customerNo, String bindPhone) {
        String key = customerNo + "_" + bindPhone;
        LOGGER.debug("{}:智能坐席增加通话数", key);
        Object obj = CacheUtil.get(CacheUtil.CACHE_CONNECT_NAME, key);
        if (obj != null) {
            AtomicInteger count = (AtomicInteger) obj;
            LOGGER.debug("{} :智能坐席增加前通话数: {}", key, count);
            int connectCount = count.incrementAndGet();
            LOGGER.debug("{} :智能坐席增加后通话数: {}", key, connectCount);
        }
    }

    public void subtractConnect(String bindPhoneId) {
        String key = this.getConnectKey(bindPhoneId);
        LOGGER.debug("{}:坐席减少通话数", bindPhoneId);
        Object obj = CacheUtil.get(CacheUtil.CACHE_CONNECT_NAME, key);
        if (obj != null) {
            AtomicInteger count = (AtomicInteger) obj;
            LOGGER.debug("{} :坐席减少前通话数：{}", bindPhoneId, count);
            int connectCount = count.decrementAndGet();
            LOGGER.debug("{} :坐席减少后通话数：{}", bindPhoneId, connectCount);
            if (connectCount == 0) {
                CacheUtil.remove(CacheUtil.CACHE_CONNECT_NAME, key);
            }
        }
    }
    
    public void subtractRobotConnect(String customerNo, String bindPhone) {
        String key = customerNo + "_" + bindPhone;
        LOGGER.debug("{}:坐席减少通话数", key);
        Object obj = CacheUtil.get(CacheUtil.CACHE_CONNECT_NAME, key);
        if (obj != null) {
            AtomicInteger count = (AtomicInteger) obj;
            LOGGER.debug("{} :坐席减少前通话数：{}", key, count);
            int connectCount = count.decrementAndGet();
            LOGGER.debug("{} :坐席减少后通话数：{}", key, connectCount);
            if (connectCount == 0) {
                CacheUtil.remove(CacheUtil.CACHE_CONNECT_NAME, key);
            }
        }
    }

    public String getConnectKey(String bindPhoneId) {
        String key = null;
        BindPhone phone = this.baseService.getBindPhone(bindPhoneId);
        if (this.commonSettings.getSwitchboardType().equals(
                BusinessConstants.GLOABLE_SWITCHBOARD_TYPE)) {
            key = phone.getCustomerNo() + EXP + phone.getOrigBindPhoneNo();
        } else if (this.commonSettings.getSwitchboardType().equals(
                BusinessConstants.SEPARATE_SWITCHBOARD_TYPE)) {
            key = bindPhoneId;
        }
        return key;
    }

    public void subtractGloableConnect(String customerNo, String bindPhone) {
        if (this.commonSettings.getSwitchboardType().equals(
                BusinessConstants.GLOABLE_SWITCHBOARD_TYPE)) {
            String key = customerNo + EXP + bindPhone;
            Object obj = CacheUtil.get(CacheUtil.CACHE_CONNECT_NAME, key);
            if (obj != null) {
                AtomicInteger count = (AtomicInteger) obj;
                LOGGER.debug("{} :全局减少前通话数：{}", bindPhone, count);
                int connectCount = count.decrementAndGet();
                LOGGER.debug("{} :全局减少后通话数：{}", bindPhone, connectCount);
                if (connectCount == 0) {
                    CacheUtil.remove(CacheUtil.CACHE_CONNECT_NAME, key);
                }
            }
        } else {
            LOGGER.warn("中继非全局模式，不能通过号码减少通话数");
        }
    }

    public void saveTalkTask(OutDataTask task) {
        try {
            String filePath = null;
            //如果保存的是录音和留言，保存上传文件任务
            String customerNo = null;
            if (task.getType() == OutDataType.VOICE) {
                VoiceRecord v = this.getVoiceRecordByTalkNoteId(task.getTaskId());
                filePath = v.getFilePath();
                customerNo = v.getCustomerNo();
            } else if (task.getType() == OutDataType.VOICE_BOX) {
                VoiceBox v = this.getVoiceBoxByTalkNoteId(task.getTaskId());
                filePath = v.getFilePath();
                customerNo = v.getCustomerNo();
            } else if (task.getType() == OutDataType.TALK) {
                TalkNote v = talkNoteDao.findOne(task.getTaskId());
                customerNo = v.getCustomerNo();
            } else if (task.getType() == OutDataType.VOICE_SCORE) {
                VoiceScore v = this.getVoiceScoreByTalkNoteId(task.getTaskId());
                customerNo = v.getCustomerNo();
            }
            int status = 0;
            int count = uploadNumberDao.countByCustomerNo(customerNo);
            LOGGER.info("{}:任务是否延迟执行:{}", (count > 0 ? "是" : "否"));
            if (count > 0) {
                status = 999;
            }
            LOGGER.info("FilePath:{}", filePath);
            if (filePath != null) {
                FileTask fileTask = new FileTask(task.getTaskId(), filePath);
                fileTask.setStatus(status);
                this.fileTaskDao.save(fileTask);
                LOGGER.debug("创建文件任务,id={}", fileTask.getId());
            }
            if(task.getType() == OutDataType.TALK) {
                //只保存话单任务，录音、留言、满意度不再保存任务
                task.setStatus(status);
                task.setCreateTime(new Date());
                this.outDataTaskDao.save(task);
            }
        } catch (Exception e) {
            LOGGER.error("创建文件任务:", e);
        }
    }

    public void intoOrder(String number, String bindLsh) {
        String key = number + bindLsh;
        boolean contains = ORDER_MAP.containsKey(key);
        ORDER_MAP.put(key, !contains);
    }
    
    public List<BindPhone> findMultipleBindPhone(String bindPhone, String uniqueName, String excludeNo) {
        return this.bindRepository.findByUniqueName(bindPhone, uniqueName, excludeNo);
    }
    
    public List<BindPhone> findMultipleBindPhone(String bindPhone, String uniqueName) {
        return this.bindRepository.findByUniqueName(bindPhone, uniqueName);
    }
}
