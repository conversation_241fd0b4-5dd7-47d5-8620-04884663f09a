/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.rmi;

import java.util.List;

import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.WhiteList;

/**
 * <AUTHOR>
 */
public interface RmiService {

    public String hello();

    public int modifyAgentStatus(String customerNo, String lsh,
        String agengtStatus);
	
	public void modifyAgentStatusByAgentId(String customerNo, String agentId, String agengtStatus);

    public List<BindPhone> getIdleAgent(String customer, String deptLsh,
        String callerNo);

    public User getUserByNumber(String number);

    public BindPhone getBindPhoneByCustomerNoAndLsh(String customerNo,
        String lsh);

    public String getAgentStatus(String customerNo, String lsh);
	
	public String getAgentStatusByAgentId(String agentId);

    public void modifyAgentCallNum(String customerNo, String lsh);

    public void createQueue(String customerNo, String deptLsh);
    
    public String getQueueInfo(String customerNo, String deptLsh);

    public int getQueueSizeByDept(String customerNo, String deptLsh);

    public int addToQueue(String customerNo, String deptLsh, String callerId);

    public void updateQueueState(String customerNo, String deptLsh, String callerId, int queueState);
    
    public boolean queueExist(String customerNo, String deptLsh, String callerId);
    
    public int removeQueue(String customerNo, String deptLsh, String callerId,
        boolean spreadState);

    public int continueWait(String customerNo, String deptLsh, String callerId);

    public void MessageAgentOnHook(String customerNo, String deptLsh);

    public void MessageCallerOnHook(String customerNo, String deptLsh,
        String callerId);

    public int[] getQueueStatus(String customerNo, String deptLsh,
        String callerId);

    public String getAgentStatus(String customerNo, String agentId, String lsh);

    public int modifyAgentStatusAndNeedlogin(String customerNo, String lsh,
        String status, boolean isNeedlogin);

    public void modifyAgentNeedlogin(String customerNo, String lsh, boolean isNeedlogin);
    
    public void addConnect(String customerNo, String lsh);
    
    public void addRobotConnect(String customerNo, String bindPhone);

    public int getConnectCount(String customerNo, String lsh);
    
    public int getRobotConnectCount(String customerNo, String bindPhone);

    public void subtractConnect(String customerNo, String lsh);
    
    public void subtractRobotConnect(String customerNo, String bindPhone);
    
    public void subtractGloableConnect(String customerNo, String bindPhone);

    public BindPhone getBindPhoneByCustomerNoAndReportNum(String customerNo,
        String reportNum);

    public boolean isBindPhoneEmpty(String customerNo, String deptLsh);
    
    public void notifyQueueAgentOnhook(String customerNo, String agentId);
    
    public void startResetAgentStatus(String currentStatus);
	
	public String getDeptBindphoneDetails(String customerNo, String deptLsh);
    
    public String getDeptLastBindPhone(String customNo, String deptLsh);
    
    public void updateDeptLastBindPhone(String customNo, String deptLsh, String bindPhoneLsh);
    
    public int getQueueIndex(String customerNo, String deptLsh, String callId);
    
    public void addOfflineCache(String numberCode, String deptLsh, String caller);
    
    public long checkCustomerWhiteList(String numberCode, String caller);
    
    public WhiteList subtractWhiteListCount(String numberCode, Long xh);
    
    public int batchModifyAgentStatus(String customerNo, String lsh, String agengtStatus, String uniqueName);
    
    public void modifyBatchAgentNeedlogin(String customerNo, String lsh, boolean isNeedlogin, String uniqueName);
    
    public int modifyBatchAgentStatusAndNeedlogin(String customerNo, String lsh, String status, boolean isNeedlogin, String uniqueName);
    
    public int batchModifyAgentStatusByBindphone(String bindPhone, String agengtStatus, String uniqueName);
}
