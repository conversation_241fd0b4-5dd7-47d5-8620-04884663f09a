/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.rmi.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.WhiteList;
import com.sungoin.netphoneLocal.business.rmi.RmiService;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.business.service.BusinessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> 2015-8-12
 */
@Service(value = "RmiService")
public class RmiServiceImpl implements RmiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RmiServiceImpl.class);
    private static final String REMOTE_FINISH = "远程调用结束";

    @Resource
    private BaseService baseService;

    @Resource
    private BusinessService businessService;

    @Override
    public String hello() {
        return "hello world";
    }

    @Override
    public int modifyAgentStatus(String customerNo, String lsh, String agengtStatus) {
        LOGGER.debug("备用提交更新坐席状态：400号码：{}, 绑定号码流水号：{}, 坐席状态：{}", customerNo, lsh, agengtStatus);
        int i = this.baseService.modifyAgentStatus(customerNo, lsh, agengtStatus);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public List<BindPhone> getIdleAgent(String customerNo, String deptLsh, String callerNo) {
        LOGGER.debug("备用查询空闲坐席：400号码：{}, 部门流水号：{}, 主叫号码：{}", customerNo, deptLsh, callerNo);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        List<BindPhone> l = this.baseService.getIdleAgent(dept, callerNo);
        LOGGER.debug(REMOTE_FINISH);
        return l;
    }

    @Override
    public User getUserByNumber(String number) {
        LOGGER.debug("备用查询用户信息：400号码：{}", number);
        User u = this.baseService.getUserByNumber(number);
        LOGGER.debug(REMOTE_FINISH);
        return u;
    }

    @Override
    public BindPhone getBindPhoneByCustomerNoAndLsh(String customerNo, String lsh) {
        LOGGER.debug("备用查询绑定号码：400号码：{}, 绑定号码流水号：{}", customerNo, lsh);
        BindPhone b = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        LOGGER.debug(REMOTE_FINISH);
        return b;
    }

    @Override
    public String getAgentStatus(String customerNo, String lsh) {
        LOGGER.debug("备用查询坐席状态：400号码：{}, 绑定号码流水号：{}", customerNo, lsh);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        String s = this.baseService.getAgentStatus(phone.getId());
        LOGGER.debug(REMOTE_FINISH);
        return s;
    }

    @Override
    public void modifyAgentCallNum(String customerNo, String lsh) {
        LOGGER.debug("备用更新坐席接听次数：400号码：{}, 绑定号码流水号：{}", customerNo, lsh);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        this.baseService.modifyAgentCallNum(phone.getId());
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public void createQueue(String customerNo, String deptLsh) {
        LOGGER.debug("备用创建队列：400号码：{}, 部门流水号：{}", customerNo, deptLsh);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        this.businessService.createQueue(dept.getId());
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public int getQueueSizeByDept(String customerNo, String deptLsh) {
        LOGGER.debug("备用获取队列大小：400号码：{}, 部门流水号：{}", customerNo, deptLsh);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        int i = this.businessService.getQueueSizeByDept(dept.getId());
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public int addToQueue(String customerNo, String deptLsh, String callerId) {
        LOGGER.debug("备用加入队列：400号码：{}, 部门流水号：{}，主叫ID：{}", customerNo, deptLsh, callerId);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        int i = this.businessService.addToQueue(dept.getId(), callerId);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public int removeQueue(String customerNo, String deptLsh, String callerId, boolean spreadState) {
        LOGGER.debug("备用移除队列：400号码：{}, 部门流水号：{}，主叫ID：{}", customerNo, deptLsh, callerId);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        int i = this.businessService.removeQueue(dept.getId(), callerId, spreadState);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public int continueWait(String customerNo, String deptLsh, String callerId) {
        LOGGER.debug("备用请求队列继续等待：400号码：{}, 部门流水号：{}，主叫ID：{}", customerNo, deptLsh, callerId);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        int i = this.businessService.continueWait(dept.getId(), callerId);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public void MessageAgentOnHook(String customerNo, String deptLsh) {
        LOGGER.debug("备用通知坐席空闲：400号码：{}, 部门流水号：{}", customerNo, deptLsh);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        this.businessService.MessageAgentOnHook(dept.getId());
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public void MessageCallerOnHook(String customerNo, String deptLsh, String callerId) {
        LOGGER.debug("备用通知坐席空闲：400号码：{}, 部门流水号：{}，主叫ID：{}", customerNo, deptLsh, callerId);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        this.businessService.MessageCallerOnHook(dept.getId(), callerId);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public int[] getQueueStatus(String customerNo, String deptLsh, String callerId) {
        LOGGER.debug("备用通知坐席空闲：400号码：{}, 部门流水号：{}，主叫ID：{}", customerNo, deptLsh, callerId);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        int[] i = this.businessService.getQueueStatus(dept.getId(), callerId);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public String getAgentStatus(String customerNo, String agentId, String lsh) {
        LOGGER.debug("备用获取坐席状态：400号码：{}, 坐席ID：{}，绑定号码流水号：{}", customerNo, agentId, lsh);
        String s = this.baseService.getAgentStatus(customerNo, agentId, lsh);
        LOGGER.debug(REMOTE_FINISH);
        return s;
    }

    @Override
    public int modifyAgentStatusAndNeedlogin(String customerNo, String lsh, String status, boolean isNeedlogin) {
        LOGGER.debug("备用提交更新坐席状态和登录标识：400号码：{}, 绑定号码流水号：{}, 坐席状态：{}, 登录标识：{}", customerNo, lsh, status, isNeedlogin);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh( customerNo, lsh);
        int i = this.baseService.modifyAgentStatusAndNeedlogin(phone.getId(), status, isNeedlogin);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public void modifyAgentNeedlogin(String customerNo, String lsh, boolean isNeedlogin) {
        LOGGER.debug("备用更新坐席登录状态：400号码：{}, 绑定号码流水号：{}，登录标识：{}", customerNo, lsh, isNeedlogin);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        this.baseService.modifyAgentNeedlogin(phone.getId(), isNeedlogin);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public void addConnect(String customerNo, String lsh) {
        LOGGER.debug("备用增加中继通话数：400号码：{}, 绑定号码流水号：{}", customerNo, lsh);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        this.businessService.addConnect(phone.getId());
        LOGGER.debug(REMOTE_FINISH);
    }
    
    @Override
    public void addRobotConnect(String customerNo, String bindPhone) {
        LOGGER.debug("备用增加智能坐席中继通话数：400号码：{}, 绑定号码：{}", customerNo, bindPhone);
        this.businessService.addRobotConnect(customerNo, bindPhone);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public int getConnectCount(String customerNo, String lsh) {
        LOGGER.debug("备用获取中继通话数：400号码：{}, 绑定号码流水号：{}", customerNo, lsh);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        int i = this.businessService.getConnectCount(phone.getId());
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }
    
    @Override
    public int getRobotConnectCount(String customerNo, String bindPhone) {
        LOGGER.debug("备用获取智能客服中继通话数：400号码：{}, 绑定号码：{}", customerNo, bindPhone);
        int i = this.businessService.getRobotConnectCount(customerNo, bindPhone);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public void subtractConnect(String customerNo, String lsh) {
        LOGGER.debug("备用减少中继通话数：400号码：{}, 绑定号码流水号：{}", customerNo, lsh);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        this.businessService.subtractConnect(phone.getId());
        LOGGER.debug(REMOTE_FINISH);
    }
    
    @Override
    public void subtractRobotConnect(String customerNo, String bindPhone) {
        LOGGER.debug("备用减少中继通话数：400号码：{}, 绑定号码：{}", customerNo, bindPhone);
        this.businessService.subtractRobotConnect(customerNo, bindPhone);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public void subtractGloableConnect(String customerNo, String bindPhone) {
        LOGGER.debug("备用增加全局中继通话数：400号码：{}, 绑定号码流水号：{}", customerNo, bindPhone);
        this.businessService.subtractGloableConnect(customerNo, bindPhone);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public BindPhone getBindPhoneByCustomerNoAndReportNum(String customerNo, String reportNum) {
        LOGGER.debug("备用获取绑定号码：400号码：{}, 工号：{}", customerNo, reportNum);
        BindPhone b = this.baseService.getBindPhoneByCustomerNoAndReportNum(customerNo, reportNum);
        LOGGER.debug(REMOTE_FINISH);
        return b;
    }

    @Override
    public boolean isBindPhoneEmpty(String customerNo, String deptLsh) {
        LOGGER.debug("备用获取部门下是否无绑定号码：400号码：{}, 部门流水号：{}", customerNo, deptLsh);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        boolean b = this.baseService.isBindPhoneEmpty(dept.getId());
        LOGGER.debug(REMOTE_FINISH);
        return b;
    }

    @Override
    public void notifyQueueAgentOnhook(String customerNo, String agentId) {
        LOGGER.debug("备用通知坐席挂机：400号码：{}, 坐席ID：{}", customerNo, agentId);
        this.businessService.notifyQueueAgentOnhook(customerNo, agentId);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public void startResetAgentStatus(String currentStatus) {
        LOGGER.debug("备用重置坐席状态：当前状态：{}", currentStatus);
        this.baseService.startResetAgentStatus(currentStatus);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public void modifyAgentStatusByAgentId(String customerNo, String agentId, String agengtStatus) {
        LOGGER.debug("备用提交更新坐席状态：400号码：{}, 坐席ID：{}, 坐席状态：{}", customerNo, agentId, agengtStatus);
        this.baseService.modifyAgentStatusByAgentId(customerNo, agentId, agengtStatus);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public String getAgentStatusByAgentId(String agentId) {
        LOGGER.debug("备用获取坐席状态：坐席ID：{}", agentId);
        String s = this.baseService.getAgentStatusByAgentId(agentId);
        LOGGER.debug(REMOTE_FINISH);
        return s;
    }

    @Override
    public String getDeptBindphoneDetails(String customerNo, String deptLsh) {
        LOGGER.debug("备用获取部门绑定号码明细：400号码：{}，部门流水号：{}", customerNo, deptLsh);
        String s = this.baseService.getDeptBindphoneDetails(customerNo, deptLsh);
        LOGGER.debug(REMOTE_FINISH);
        return s;
    }

    @Override
    public String getDeptLastBindPhone(String customNo, String deptLsh) {
        LOGGER.debug("备用获取部门最后接听绑定号码：400号码：{}，部门流水号：{}", customNo, deptLsh);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customNo, deptLsh);
        String s = dept.getLastBindPhoneLsh();
        LOGGER.debug(REMOTE_FINISH);
        return s;
    }

    @Override
    public void updateDeptLastBindPhone(String customNo, String deptLsh, String bindPhoneLsh) {
        LOGGER.debug("备用更新部门最后接听绑定号码：400号码：{}，部门流水号：{}，绑定号码流水号：{}", customNo, deptLsh, bindPhoneLsh);
        this.baseService.updateDeptLastBindPhone(customNo, deptLsh, bindPhoneLsh);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public int getQueueIndex(String customerNo, String deptLsh, String callId) {
        LOGGER.debug("备用获取队列索引：400号码：{}，部门流水号：{}，主叫ID：{}", customerNo, deptLsh, callId);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        int i = this.businessService.getQueueIndex(dept.getId(), callId);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public void addOfflineCache(String numberCode, String deptLsh, String caller) {
        LOGGER.debug("备用增加离线队列缓存：400号码：{}，部门流水号：{}，主叫ID：{}", numberCode, deptLsh, caller);
        this.baseService.addOfflineCache(numberCode, deptLsh, caller);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public long checkCustomerWhiteList(String numberCode, String caller) {
        LOGGER.debug("备用检查白名单：400号码：{}，主叫ID：{}", numberCode,  caller);
        long l = this.baseService.checkCustomerWhiteList(numberCode, caller);
        LOGGER.debug(REMOTE_FINISH);
        return l;
    }

    @Override
    public WhiteList subtractWhiteListCount(String numberCode, Long xh) {
        LOGGER.debug("备用减少白名单次数：400号码：{}，序号：{}", numberCode,  xh);
        WhiteList w = this.baseService.subtractWhiteListCount(numberCode, xh);
        LOGGER.debug(REMOTE_FINISH);
        return w;
    }

    @Override
    public int batchModifyAgentStatus(String customerNo, String lsh, String agengtStatus, String uniqueName) {
        LOGGER.debug("备用提交批量更新坐席状态：400号码：{}, 绑定号码流水号：{}, 坐席状态：{}， uniqueName：{}", customerNo, lsh, agengtStatus, uniqueName);
        int i = this.baseService.batchModifyAgentStatus(customerNo, lsh, agengtStatus, uniqueName);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public void modifyBatchAgentNeedlogin(String customerNo, String lsh, boolean isNeedlogin, String uniqueName) {
        LOGGER.debug("备用提交批量更新坐席登录标识：400号码：{}, 绑定号码流水号：{}, 登录标识：{}， uniqueName：{}", customerNo, lsh, isNeedlogin, uniqueName);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        this.baseService.modifyBatchAgentNeedlogin(phone.getId(), isNeedlogin, uniqueName);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public int modifyBatchAgentStatusAndNeedlogin(String customerNo, String lsh, String status, boolean isNeedlogin, String uniqueName) {
        LOGGER.debug("备用提交批量更新坐席登录标识：400号码：{}, 绑定号码流水号：{}, 坐席状态：{}， 登录标识：{}， uniqueName：{}", customerNo, lsh, status, isNeedlogin, uniqueName);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        int i = this.baseService.modifyBatchAgentStatusAndNeedlogin(phone.getId(), status, isNeedlogin, uniqueName);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public int batchModifyAgentStatusByBindphone(String bindPhone, String agengtStatus, String uniqueName) {
        LOGGER.debug("备用提交批量更新坐席状态： 绑定号码：{}, 坐席状态：{}， uniqueName：{}", bindPhone,  agengtStatus, uniqueName);
        int i = this.baseService.batchModifyAgentStatusByBindphone(bindPhone, agengtStatus, uniqueName);
        LOGGER.debug(REMOTE_FINISH);
        return i;
    }

    @Override
    public void updateQueueState(String customerNo, String deptLsh, String callerId, int queueState) {
        LOGGER.debug("备用更新队列状态： 400号码：{}, 部门流水号：{}， 主叫ID：{}， 队列状态：{}", customerNo, deptLsh,  callerId, queueState);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        this.businessService.updateQueueState(dept.getId(), callerId, queueState);
        LOGGER.debug(REMOTE_FINISH);
    }

    @Override
    public boolean queueExist(String customerNo, String deptLsh, String callerId) {
        LOGGER.debug("备用退出队列： 400号码：{}, 部门流水号：{}， 主叫ID：{}", customerNo, deptLsh,  callerId);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        boolean b = this.businessService.queueExist(dept.getId(), callerId);
        LOGGER.debug(REMOTE_FINISH);
        return b;
    }

    @Override
    public String getQueueInfo(String customerNo, String deptLsh) {
        LOGGER.debug("备用获取队列明细： 400号码：{}, 部门流水号：{}", customerNo, deptLsh);
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
        String s = this.businessService.getQueueInfo(dept.getId());
        LOGGER.debug(REMOTE_FINISH);
        return s;
    }

}
