/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.sungoin.netphoneLocal.business.bean.WeekEnum;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.DateTimeUtil.Pattern;
import java.util.Collections;

import java.util.stream.Collectors;

import javax.persistence.ConstraintMode;
import javax.persistence.ForeignKey;
import javax.persistence.Transient;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

/**
 * <AUTHOR> 2015-7-20
 * alter table t_base_dept modify column order_by bigint(20);
 * alter table t_base_bind_phone modify column order_by bigint(20);
 * alter table t_base_black_list modify column xh bigint(20);
 * alter table t_base_white_list modify column xh bigint(20);
 * alter table t_base_crbt_ring add column template_id varchar(255);
 * alter table t_base_dept add column IVR_SMS_FLAG bit;
 * alter table t_base_user add column SMS_FLAG bit;
 * alert table t_base_user add column SET_ACS_TYPE bit;
 * wrapper.java.classpath.84=%REPO_DIR%/ons-client-1.8.4.Final.jar
 * wrapper.java.classpath.85=%REPO_DIR%/sungoin-comm-1.1.62.jar
 * 
 * alter table t_base_dept add column AREA_CODE varchar(8);
 * alter table t_base_dept add column SYNC_USER_DATA bit;
 */
@Entity
@Table(name = "T_BASE_DEPT", indexes = { @Index(columnList = "CUSTOMER_NO") })
public class Dept extends IDComparator {

    private static final long serialVersionUID = -4661033105223675917L;

    public Dept() {
    }

    public int getDeptHashCode() {
        int hash = 7;
        hash = 17 * hash + Objects.hashCode(this.customerNo);
        hash = 17 * hash + Objects.hashCode(this.deptLsh);
        hash = 17 * hash + Objects.hashCode(this.name);
        hash = 17 * hash + (this.ghFlag ? 1 : 0);
        hash = 17 * hash + (this.satiFlag ? 1 : 0);
        hash = 17 * hash + (this.voiceBoxFlag ? 1 : 0);
        hash = 17 * hash + (this.voiceCodeFlag ? 1 : 0);
        hash = 17 * hash + (this.queueWaitFlag ? 1 : 0);
        hash = 17 * hash + Objects.hashCode(this.ivrKey);
        hash = 17 * hash + Objects.hashCode(this.callModle);
        hash = 17 * hash + Objects.hashCode(this.timeModle);
        hash = 17 * hash + Objects.hashCode(this.areaModle);
        hash = 17 * hash + Objects.hashCode(this.maxQueueCount);
        hash = 17 * hash + Objects.hashCode(this.queueTimeout);
        hash = 17 * hash + Objects.hashCode(this.nextDelayTime);
        hash = 17 * hash + Objects.hashCode(this.agentWaitTime);
        hash = 17 * hash + Objects.hashCode(this.createTime);
        hash = 17 * hash + Objects.hashCode(this.colorRingLsh);
        hash = 17 * hash + Objects.hashCode(this.parentDeptLsh);
        hash = 17 * hash + Objects.hashCode(this.childKeyLen);
        hash = 17 * hash + Objects.hashCode(this.depth);
        hash = 17 * hash + Objects.hashCode(this.childinType);
        hash = 17 * hash + Objects.hashCode(this.queueWaitFlag);
        hash = 17 * hash + Objects.hashCode(this.orderBy);
        hash = 17 * hash + Objects.hashCode(this.salesAlertToneLsh);
        hash = 17 * hash + Objects.hashCode(this.satiRingLsh);
        hash = 17 * hash + Objects.hashCode(this.loopPlayRing);
        hash = 17 * hash + Objects.hashCode(this.averageType);
        hash = 17 * hash + Objects.hashCode(this.memoryFlag);
        hash = 17 * hash + Objects.hashCode(this.salesAlertToneLsh);
        hash = 17 * hash + Objects.hashCode(this.resetRingLsh);
        return hash;
    }

    public void initParpams(String customerNo, String deptLsh, String name, int callModle, Date createTime,
        String parentDeptLsh, String ivrKey, String colorRingLsh, int childKeyLen, boolean voiceCodeFlag, int depth,
        int childinType, int timeModle, String areaModle, int maxQueueCount, int queueTimeout, int agentWaitTime,
        int nextDelayTime, boolean satiFlag, boolean ghFlag, boolean voiceBoxFlag, boolean queueWaitFlag, Long orderBy,
        String salesAlertToneLsh, String satiRingLsh, Boolean loopPlayRing, int averageType,Boolean memoryFlag,
        String companyRingLsh,String resetRingLsh) {
        this.customerNo = customerNo;
        this.deptLsh = deptLsh;
        this.name = name;
        this.callModle = callModle;
        this.createTime = createTime;
        this.parentDeptLsh = parentDeptLsh;
        this.ivrKey = ivrKey;
        this.colorRingLsh = colorRingLsh;
        this.childKeyLen = childKeyLen;
        this.voiceCodeFlag = voiceCodeFlag;
        this.depth = depth;
        this.childinType = childinType;
        this.timeModle = timeModle;
        this.areaModle = areaModle;
        this.maxQueueCount = maxQueueCount;
        this.queueTimeout = queueTimeout;
        this.agentWaitTime = agentWaitTime;
        this.nextDelayTime = nextDelayTime;
        this.satiFlag = satiFlag;
        this.ghFlag = ghFlag;
        this.voiceBoxFlag = voiceBoxFlag;
        this.queueWaitFlag = queueWaitFlag;
        this.orderBy = orderBy;
        this.salesAlertToneLsh = salesAlertToneLsh;
        this.satiRingLsh = satiRingLsh;
        this.loopPlayRing = loopPlayRing;
        this.averageType = averageType;
        this.memoryFlag = memoryFlag;
        this.companyRingLsh = companyRingLsh;
        this.resetRingLsh = resetRingLsh;
    }

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 400号码.
     */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /**
     * 部门Lsh.
     */
    @Column(name = "DEPT_LSH")
    private String deptLsh;

    /**
     * 部门名称.
     */
    @Column(name = "DEPT_NAME")
    private String name;

    /**
     * 彩铃.
     */
    @Column(name = "colorring_flag")
    private boolean colorringFlag;

    /**
     * 工号.
     */
    @Column(name = "gh_flag")
    private boolean ghFlag;

    /**
     * 满意度.
     */
    @Column(name = "sati_flag")
    private boolean satiFlag;

    /**
     * 录音.
     */
    @Column(name = "record_flag")
    private boolean recordFlag;

    /**
     * 留言.
     */
    @Column(name = "voicebox_flag")
    private boolean voiceBoxFlag;

    /**
     * 防伪码.
     */
    @Column(name = "voicecode_flag")
    private boolean voiceCodeFlag;

    /**
     * 中继.
     */
    @Column(name = "line_flag")
    private boolean lineFlag;

    /**
     * 来电弹屏.
     */
    @Column(name = "screen_flag")
    private boolean screenFlag;

    /**
     * 总机.
     */
    @Column(name = "exchange_flag")
    private boolean exchangeFlag;

    /**
     * 导航键.
     */
    @Column(name = "IVR_KEY")
    private String ivrKey;

    /**
     * 下级部门按键长度.
     */
    @Column(name = "CHILD_KEY_LEN")
    private Integer childKeyLen;

    /**
     * 呼叫模式:0.顺序呼,1.随机呼 2-平均分配 3-轮训接听.
     */
    @Column(name = "CALL_MODLE")
    private Integer callModle;

    /**
     * 平均类型:0.按天,1.按周 2.按月
     */
    @Column(name = "AVERAGE_TYPE")
    private Integer averageType;

    /**
     * 导航类型 0-按键 1-分流.
     */
    @Column(name = "CHILDINTYPE")
    private Integer childinType;

    /**
     * 时间策略：1.全部,2.周一到周五,3.星期,9.自定义,10.节假日,11.工作日
     */
    @Column(name = "TIME_MODLE")
    private Integer timeModle;

    /**
     * 地区策略.
     */
    @Lob
    @Column(name = "AREA_MODLE")
    private String areaModle;

    /**
     * 所处层数.
     */
    @Column(name = "DEPTH")
    private Integer depth;

    /**
     * 最大排队数.
     */
    @Column(name = "MAX_QUEUE_COUNT")
    private Integer maxQueueCount;

    /**
     * 排队时长.
     */
    @Column(name = "QUEUE_TIMEOUT")
    private Integer queueTimeout;

    /**
     * 下一轮延迟时间 默认5秒.
     */
    @Column(name = "NEXT_DELAY_TIME")
    private Integer nextDelayTime = 5;

    /**
     * 坐席等待时长.
     */
    @Column(name = "AGENT_WAIT_TIME")
    private Integer agentWaitTime;

    /**
     * 排队超时等待 0-未开通 1-开通.
     */
    @Column(name = "QUEUE_WAIT_FLAG")
    private boolean queueWaitFlag;

    /**
     * 排序.
     */
    @Column(name = "ORDER_BY")
    private Long orderBy;

    /**
     * 创建时间.
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 创建人.
     */
    @Column(name = "CREATE_PERSON")
    private String createPerson;

    /**
     * 彩铃流水号 如果colorRing为空，且colorRingLsh不为空 查询一次colorRing，查到更新colorRing否则
     * 清空colorRingLsh（数据同步时可能会先插入部门再插入彩铃） .
     */
    @Column(name = "COLOR_RING_LSH")
    private String colorRingLsh;

    /**
     * 坐席提示音流水号 如果agentAlertRing为空，且agentAlertRingLsh不为空
     * 查询一次agentAlertRing，查到更新agentAlertRing否则
     * 清空agentAlertRingLsh（数据同步时可能会先插入部门再插入坐席提示音） .
     */
    @Column(name = "SALESALERTTONE_LSH")
    private String salesAlertToneLsh;

    /**
     * 上级部门流水号 如果parentDept为空，且parentDeptLsh不为空 查询一次parentDept，查到更新parentDept
     * （数据同步时可能会先插入子部门再插入父部门） .
     */
    @Column(name = "PARENT_DEPT_LSH")
    private String parentDeptLsh;

    /**
     * 对应400.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private User user;

    /**
     * 父级部门.
     */
    @ManyToOne
    private Dept parentDept;

    /**
     * 对应彩铃.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "COLOR_RING_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ColorRing colorRing;

    /**
     * 坐席提示彩铃.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "AGENT_COLOR_RING_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ColorRing agentAlertRing;

    /**
     * 子部门.
     */
    @OrderBy(value = "orderBy asc")
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "parentDept")
    private List<Dept> childDepts = new ArrayList<Dept>();

    /** 满意度流水号. */
    @Column(name = "SATI_RING_LSH")
    private String satiRingLsh;

    /**
     * 满意度提示彩铃.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "SATI_COLOR_RING_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ColorRing satiRing;

    /**
     * 是否循环播放彩铃音.
     */
    @Column(name = "LOOP_PLAY_RING")
    private Boolean loopPlayRing;
    
    /**
     * 来电记忆功能.
     */
    @Column(name = "MEMORY_FLAG")
    private Boolean memoryFlag;
    
    
    /** 企业宣传彩铃流水号. */
    @Column(name = "COMPANY_RING_LSH")
    private String companyRingLsh;

    /**
     * 企业宣传提示彩铃.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "COMPANY_COLOR_RING_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ColorRing companyRing;
    
    /** 按键重放彩铃流水号. */
    @Column(name = "RESET_RING_LSH")
    private String resetRingLsh;

    /**
     * 按键重放提示彩铃.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "RESET_COLOR_RING_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ColorRing resetRing;
	
	/**
     * 语音信箱提示彩铃.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "VOICE_COLOR_RING_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ColorRing voiceRing;
	
	/** 按键重放彩铃流水号. */
    @Column(name = "VOICE_RING_LSH")
    private String voiceRingLsh;
	
	/** 导航停留时长 */
    @Column(name = "IVR_STAY_TIME")
	private Integer ivrStayTime;
    
    /** 语音播报. */
    @Column(name = "SMART_BROAD_FLAG")
    private Boolean smartBroadFlag;
    
    /** 语音播报彩铃流水号. */
    @Column(name = "SMART_BROAD_RING_LSH")
    private String smartBroadRingLsh;
    
    /** 语音播报彩铃. */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "SMART_BROAD_RING_ID")
    private ColorRing smartBroadRing;
    
    /**
     * 最后接听的绑定号码流水号
     * ALTER TABLE t_base_dept ADD last_bind_phone_lsh VARCHAR(255);
     */
    @Column(name = "LAST_BIND_PHONE_LSH")
    private String lastBindPhoneLsh;
    
    /** 导航短信开关. */
    @Column(name = "IVR_SMS_FLAG")
    private Boolean ivrSmsFlag;
    
    /** 排队等待彩铃流水号. */
    @Column(name = "QUEUE_WAIT_RING_LSH")
    private String queueWaitRingLsh;
    
    /** 排队等待彩铃. */
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name = "QUEUE_WAIT_RING_ID")
    private ColorRing queueWaitRing;
    
    /**
     * 语音关键词
     */
    @Column(name="AUDIO_KEYWORDS")
    private String audioKeywords;

    /**
     * 区域编码
     */
    @Column(name="AREA_CODE")
    private String areaCode;
    
    /**
     * 是否同步用户数据.
     */
    @Column(name = "SYNC_USER_DATA")
    private Boolean syncUserData;
    
    public void addChildDept(Dept dept) {
        this.childDepts.add(dept);
        dept.setParentDept(this);
    }

    public void removeChildDept(Dept dept) {
        this.childDepts.remove(dept);
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "dept")
    private List<BindPhone> bindList = new ArrayList<BindPhone>();

    public void addBindPhone(BindPhone bindPhone) {
        this.bindList.add(bindPhone);
        bindPhone.setDept(this);
    }

    public void removeBindPhone(BindPhone bindPhone) {
        this.bindList.remove(bindPhone);
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "dept")
    private List<CrbtRing> crbtRingList = new ArrayList<CrbtRing>();

    public void addCrbtRing(CrbtRing crbtRing) {
        this.crbtRingList.add(crbtRing);
        crbtRing.setDept(this);
    }

    public void removeCrbtRing(CrbtRing crbtRing) {
        this.crbtRingList.remove(crbtRing);
    }

    @Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getDeptLsh() {
        return this.deptLsh;
    }

    public void setDeptLsh(String deptLsh) {
        this.deptLsh = deptLsh;
    }

    public boolean isColorringFlag() {
        return this.colorringFlag;
    }

    public void setColorringFlag(boolean colorringFlag) {
        this.colorringFlag = colorringFlag;
    }

    public boolean isGhFlag() {
        return this.ghFlag;
    }

    public void setGhFlag(boolean ghFlag) {
        this.ghFlag = ghFlag;
    }

    public boolean isSatiFlag() {
        return this.satiFlag;
    }

    public void setSatiFlag(boolean satiFlag) {
        this.satiFlag = satiFlag;
    }

    public boolean isRecordFlag() {
        return this.recordFlag;
    }

    public void setRecordFlag(boolean recordFlag) {
        this.recordFlag = recordFlag;
    }

    public boolean isVoiceBoxFlag() {
        return this.voiceBoxFlag;
    }

    public void setVoiceBoxFlag(boolean voiceBoxFlag) {
        this.voiceBoxFlag = voiceBoxFlag;
    }

    public boolean isVoiceCodeFlag() {
        return this.voiceCodeFlag;
    }

    public void setVoiceCodeFlag(boolean voiceCodeFlag) {
        this.voiceCodeFlag = voiceCodeFlag;
    }

    public boolean isLineFlag() {
        return this.lineFlag;
    }

    public void setLineFlag(boolean lineFlag) {
        this.lineFlag = lineFlag;
    }

    public boolean isScreenFlag() {
        return this.screenFlag;
    }

    public void setScreenFlag(boolean screenFlag) {
        this.screenFlag = screenFlag;
    }

    public boolean isExchangeFlag() {
        return this.exchangeFlag;
    }

    public void setExchangeFlag(boolean exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    public String getIvrKey() {
        return this.ivrKey;
    }

    public void setIvrKey(String ivrKey) {
        this.ivrKey = ivrKey;
    }

    public Integer getChildKeyLen() {
        return this.childKeyLen;
    }

    public void setChildKeyLen(Integer childKeyLen) {
        this.childKeyLen = childKeyLen;
    }

    public Integer getCallModle() {
        return this.callModle;
    }

    public void setCallModle(Integer callModle) {
        this.callModle = callModle;
    }

    public Integer getAverageType() {
        return this.averageType;
    }

    public void setAverageType(Integer averageType) {
        this.averageType = averageType;
    }

    public Integer getChildinType() {
        return this.childinType;
    }

    public void setChildinType(Integer childinType) {
        this.childinType = childinType;
    }

    public Integer getTimeModle() {
        return this.timeModle;
    }

    public void setTimeModle(Integer timeModle) {
        this.timeModle = timeModle;
    }

    public String getAreaModle() {
        return this.areaModle;
    }

    public void setAreaModle(String areaModle) {
        this.areaModle = areaModle;
    }

    public Integer getDepth() {
        return this.depth;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    public Integer getMaxQueueCount() {
        return this.maxQueueCount;
    }

    public void setMaxQueueCount(Integer maxQueueCount) {
        this.maxQueueCount = maxQueueCount;
    }

    public Integer getQueueTimeout() {
        return this.queueTimeout;
    }

    public void setQueueTimeout(Integer queueTimeout) {
        this.queueTimeout = queueTimeout;
    }

    public Integer getNextDelayTime() {
        return this.nextDelayTime;
    }

    public void setNextDelayTime(Integer nextDelayTime) {
        this.nextDelayTime = nextDelayTime;
    }

    public Integer getAgentWaitTime() {
        return this.agentWaitTime;
    }

    public void setAgentWaitTime(Integer agentWaitTime) {
        this.agentWaitTime = agentWaitTime;
    }

    public boolean isQueueWaitFlag() {
        return this.queueWaitFlag;
    }

    public void setQueueWaitFlag(boolean queueWaitFlag) {
        this.queueWaitFlag = queueWaitFlag;
    }

    public Long getOrderBy() {
        return this.orderBy == null ? 0 : this.orderBy;
    }

    public void setOrderBy(Long orderBy) {
        this.orderBy = orderBy;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreatePerson() {
        return this.createPerson;
    }

    public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson;
    }

    public ColorRing getColorRing() {
        return this.colorRing;
    }

    public void setColorRing(ColorRing colorRing) {
        this.colorRing = colorRing;
    }

    public ColorRing getAgentAlertRing() {
        return this.agentAlertRing;
    }

    public void setAgentAlertRing(ColorRing agentAlertRing) {
        this.agentAlertRing = agentAlertRing;
    }

    public String getColorRingLsh() {
        return this.colorRingLsh;
    }

    public void setColorRingLsh(String colorRingLsh) {
        this.colorRingLsh = colorRingLsh;
    }

    public String getSalesAlertToneLsh() {
        return this.salesAlertToneLsh;
    }

    public void setSalesAlertToneLsh(String salesAlertToneLsh) {
        this.salesAlertToneLsh = salesAlertToneLsh;
    }

    public String getParentDeptLsh() {
        return this.parentDeptLsh;
    }

    public void setParentDeptLsh(String parentDeptLsh) {
        this.parentDeptLsh = parentDeptLsh;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Dept getParentDept() {
        return this.parentDept;
    }

    public void setParentDept(Dept parentDept) {
        this.parentDept = parentDept;
    }

    public List<Dept> getChildDepts() {
        return this.childDepts;
    }

    public void setChildDepts(List<Dept> childDepts) {
        this.childDepts = childDepts;
    }

    public List<BindPhone> getBindList() {
        return this.bindList;
    }

    public List<BindPhone> getOnlineBindList() {
		return this.getBindList().stream().filter(bp -> !MidwareConstants.AGENT_STATE_OFFLINE.equals(bp.getStatus()))
				.collect(Collectors.toList());
    }

    public void setBindList(List<BindPhone> bindList) {
        this.bindList = bindList;
    }

    public List<CrbtRing> getCrbtRingList() {
        return this.crbtRingList;
    }

    public void setCrbtRingList(List<CrbtRing> crbtRingList) {
        this.crbtRingList = crbtRingList;
    }

    public String getSatiRingLsh() {
        return this.satiRingLsh;
    }

    public void setSatiRingLsh(String satiRingLsh) {
        this.satiRingLsh = satiRingLsh;
    }

    public ColorRing getSatiRing() {
        return this.satiRing;
    }

    public void setSatiRing(ColorRing satiRing) {
        this.satiRing = satiRing;
    }

    public Boolean isLoopPlayRing() {
        return this.loopPlayRing == null ? false : this.loopPlayRing;
    }

    public void setLoopPlayRing(Boolean loopPlayRing) {
        this.loopPlayRing = loopPlayRing;
    }

    public Boolean isMemoryFlag() {
		return this.memoryFlag == null ? false : this.memoryFlag;
	}

	public void setMemoryFlag(Boolean memoryFlag) {
		this.memoryFlag = memoryFlag;
	}

	public String getCompanyRingLsh() {
		return companyRingLsh;
	}

	public void setCompanyRingLsh(String companyRingLsh) {
		this.companyRingLsh = companyRingLsh;
	}

	public ColorRing getCompanyRing() {
		return companyRing;
	}

	public void setCompanyRing(ColorRing companyRing) {
		this.companyRing = companyRing;
	}

	public String getResetRingLsh() {
		return resetRingLsh;
	}

	public void setResetRingLsh(String resetRingLsh) {
		this.resetRingLsh = resetRingLsh;
	}

	public ColorRing getResetRing() {
		return resetRing;
	}

	public void setResetRing(ColorRing resetRing) {
		this.resetRing = resetRing;
	}
	
	public String getVoiceRingLsh() {
		return voiceRingLsh;
	}

	public void setVoiceRingLsh(String voiceRingLsh) {
		this.voiceRingLsh = voiceRingLsh;
	}

	public ColorRing getVoiceRing() {
		return voiceRing;
	}

	public void setVoiceRing(ColorRing voiceRing) {
		this.voiceRing = voiceRing;
	}

	public Integer getIvrStayTime() {
		return ivrStayTime;
	}

	public void setIvrStayTime(Integer ivrStayTime) {
		this.ivrStayTime = ivrStayTime;
	}

	public ColorRing getSmartBroadRing() {
		return smartBroadRing;
	}

	public void setSmartBroadRing(ColorRing smartBroadRing) {
		this.smartBroadRing = smartBroadRing;
	}

	public Boolean isSmartBroadFlag() {
		return smartBroadFlag == null ? false : this.smartBroadFlag;
	}

	public String getSmartBroadRingLsh() {
		return smartBroadRingLsh;
	}
	
	/**
     * 获取部门当前炫铃.(不使用模板数据)
     *
     * @return the current crbt ring
     */
    public CrbtRing getCurrentCrbtRing(boolean isHoliday) {
        Date now = new Date();
        //如果有时间段重复的，自定义>节假日>其他（随机）
//        this.crbtRingList.sort((CrbtRing r1, CrbtRing r2) -> {
//            int o1 = r1.getOrderInt();
//            int o2 = r2.getOrderInt();
//            int t = o2 - o1;
//            if (t != 0) {
//                return t;
//            }
//            return r1.getRandomChar().compareTo(r2.getRandomChar());
//        });
        List<CrbtRing> sortedList = new ArrayList<>();
        List<CrbtRing> orderList = this.crbtRingList.stream().filter(c -> c.getOrderInt() > 0).collect(Collectors.toList());
        List<CrbtRing> randomList = this.crbtRingList.stream().filter(c -> c.getOrderInt() == 0).collect(Collectors.toList());
        orderList.sort((CrbtRing r1, CrbtRing r2) -> {
            return r2.getOrderInt() - r1.getOrderInt();
        });
        Collections.shuffle(randomList);
        sortedList.addAll(orderList);
        sortedList.addAll(randomList);
        
        String nowTime = DateTimeUtil.formatShortTime(now);
        CrbtRing currentCrbtRing = null;
        int week = DateTimeUtil.getWeek(now);
        int preWeek;
        if (week != 1) {
            preWeek = week - 1;
        } else {
            preWeek = 7;
        }
        for (CrbtRing crbtRing : sortedList) {
            if(crbtRing.getTemplateId() == null) {
                if (WeekEnum.CUSTOM.getCode() == crbtRing.getWeekTitle() && now.after(DateTimeUtil.parseDate(crbtRing.getStartTime(), Pattern.SHORTDATETIME)) && now.before(DateTimeUtil.parseDate(crbtRing.getEndTime(), Pattern.SHORTDATETIME))) {
                    currentCrbtRing = crbtRing;
                    break;
                } else if (WeekEnum.HOLIDAY.getCode() == crbtRing.getWeekTitle() && isHoliday) {
                    if (DateTimeUtil.satisfyTime(nowTime, crbtRing.getStartTime(), crbtRing.getEndTime())) {
                        currentCrbtRing = crbtRing;
                        break;
                    }
                } else if (WeekEnum.NOTHOLIDAY.getCode() == crbtRing.getWeekTitle() && !isHoliday) {
                    if (DateTimeUtil.satisfyTime(nowTime, crbtRing.getStartTime(), crbtRing.getEndTime())) {
                        currentCrbtRing = crbtRing;
                        break;
                    }
                } else if (WeekEnum.ALLDAY.getCode() == crbtRing.getWeekTitle() && DateTimeUtil.satisfyTime(nowTime, crbtRing.getStartTime(), crbtRing.getEndTime())) {
                    currentCrbtRing = crbtRing;
                    break;
                } else if (WeekEnum.WORKDAY.getCode() == crbtRing.getWeekTitle() && week < WeekEnum.SATURDAY.getCode()) {
                    if (DateTimeUtil.satisfyTime(nowTime, crbtRing.getStartTime(), crbtRing.getEndTime())) {
                        currentCrbtRing = crbtRing;
                        break;
                    }
                } else if (preWeek == crbtRing.getWeekTitle() ) {
                    if (DateTimeUtil.compareToAcrossWeekTime(nowTime, crbtRing.getStartTime(), crbtRing.getEndTime())) {
                        currentCrbtRing = crbtRing;
                        break;
                    }
                } else if (week == crbtRing.getWeekTitle()) {
                    if (DateTimeUtil.compareToWeekTime(nowTime, crbtRing.getStartTime(), crbtRing.getEndTime()) || 
                            DateTimeUtil.compareToAfterWeekTime(nowTime, crbtRing.getStartTime(), crbtRing.getEndTime())) {
                        currentCrbtRing = crbtRing;
                        break;
                    }
                }
            }
        }
        return currentCrbtRing;
    }

    public Dept getChildDeptByIvrKey(String ivrKey) {
		return this.getChildDepts().stream().filter(dept -> ivrKey.equals(dept.getIvrKey())).findFirst()
				.orElse(null);
    }

    public boolean hasChildDepts() {
        return this.getChildDepts().size() > 0;
    }

    public String getCallModleDescription() {
        if (this.callModle != null) {
            switch (this.callModle) {
            case 0:
                return "顺序呼";
            case 1:
                return "随机呼";
            case 2:
                return "平均分配：" + this.getAverageTypeDesc();
            case 3:
                return "轮训接听";
            case 4:
                return "权重接听";
            case 5:
                return "等级接听";
            default:
                return "";
            }
        }
        return "";
    }

    @Override
    public String toString() {
        return "Dept [customerNo=" + this.customerNo + ", deptLsh=" + this.deptLsh + ", name=" + this.name + "]";
    }

	public boolean isRootDept() {
		return this.getParentDept() == null;
	}
	
	public boolean isBgRing() {
		if(StringUtils.isNotEmpty(this.colorRingLsh)) {
			long lsh = Long.valueOf(this.colorRingLsh);
			return lsh >= 200000000 && lsh <= 200000005;
		}
		return false;
	}
    
    public boolean isDefaultRing() {
        return StringUtils.equals(MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE, this.colorRingLsh);
    }
    
    @Transient
    private String randomChar;
    
    public String getRandomChar() {
        if(this.randomChar == null) {
            this.randomChar = RandomStringUtils.randomAlphabetic(1);
        }
        return this.randomChar;
    }
    
    public String getAverageTypeDesc() {
        if(this.averageType == null) {
            return "无平均类型";
        }
        switch(this.averageType) {
            case 0:
                return "按天平均";
            case 1:
                return "按周平均";
            case 2:
                return "按月平均";
            default:
                return "无平均类型";
        }
    }

    public String getLastBindPhoneLsh() {
        return lastBindPhoneLsh;
    }

    public void setLastBindPhoneLsh(String lastBindPhoneLsh) {
        this.lastBindPhoneLsh = lastBindPhoneLsh;
    }
    
    public List<String> getCrbtRingTemplateIds() {
        return this.crbtRingList.stream().filter(crbt -> crbt.getTemplateId() != null).map(CrbtRing::getTemplateId).collect(Collectors.toList());
    }
    
    public CrbtRing getCrbtRingByTemplateId(String templateId) {
        return this.crbtRingList.stream().filter(crbt -> templateId.equals(crbt.getTemplateId())).findAny().orElse(null);
    }
    
	public Boolean isIvrSmsFlag() {
		return ivrSmsFlag == null ? false : this.ivrSmsFlag;
	}
    
    public String getQueueWaitRingLsh() {
		return queueWaitRingLsh;
	}

	public ColorRing getQueueWaitRing() {
		return queueWaitRing;
	}

	public int getTimeModleOrderInt() {
        if(this.timeModle == null) {
            return 0;
        }
        switch (this.timeModle) {
            case 9://自定义优先级最高
                return 2;
            case 10://节假日第二
                return 1;
            default:
                return 0;
        }
    }

	public String getAudioKeywords() {
		return audioKeywords;
	}

    public String getAreaCode() {
        return areaCode;
    }
    
    public boolean isSyncUserData() {
        return this.syncUserData != null && this.syncUserData;
    }
}
